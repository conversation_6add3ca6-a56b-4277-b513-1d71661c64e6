# app.py
"""
mem0应用主文件
实现Memory实例化和核心功能函数，采用单例模式和错误处理机制
"""

import hashlib
import json
import logging
import time
from typing import Dict, List, Optional, Any

from mem0 import AsyncMemory
from mem0_config import config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量用于单例模式
_memory_client = None
_config_hash = None


def _get_config_hash(config_dict: Dict) -> str:
    """生成配置的哈希值以检测变更"""
    config_str = json.dumps(config_dict, sort_keys=True, default=str)
    return hashlib.md5(config_str.encode()).hexdigest()


async def get_memory_client() -> Optional[AsyncMemory]:
    """
    获取或初始化AsyncMemory客户端（单例模式）

    Returns:
        AsyncMemory实例或None（如果初始化失败）

    Raises:
        Exception: 如果配置缺失或初始化失败
    """
    global _memory_client, _config_hash

    try:
        # 检查配置是否变更
        current_config_hash = _get_config_hash(config)

        # 只有在配置变更或客户端不存在时才重新初始化
        if _memory_client is None or _config_hash != current_config_hash:
            logger.info(f"初始化AsyncMemory客户端，配置哈希: {current_config_hash}")

            try:
                _memory_client = await AsyncMemory.from_config(config_dict=config)

                # 🔧 修复：手动设置 node_label，确保图数据库查询正常
                if hasattr(_memory_client, 'graph') and _memory_client.graph:
                    if not _memory_client.graph.node_label and config.get('graph_store', {}).get('config', {}).get('base_label'):
                        base_label = config['graph_store']['config']['base_label']
                        _memory_client.graph.node_label = f":`{base_label}`"
                        logger.info(f"手动设置 node_label: {_memory_client.graph.node_label}")

                _config_hash = current_config_hash
                logger.info("AsyncMemory客户端初始化成功")
            except Exception as init_error:
                logger.error(f"AsyncMemory客户端初始化失败: {init_error}")
                _memory_client = None
                _config_hash = None
                return None

        return _memory_client

    except Exception as e:
        logger.error(f"获取AsyncMemory客户端时发生错误: {e}")
        return None


def reset_memory_client():
    """重置全局Memory客户端，强制重新初始化"""
    global _memory_client, _config_hash
    _memory_client = None
    _config_hash = None
    logger.info("Memory客户端已重置")


async def add_memory(text: str, user_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    智能添加记忆到mem0系统（自动进行情节处理、实体提取和智能更新）

    功能特性：
    - 自动检测相似记忆并智能合并更新
    - 支持长文本的全局实体提取
    - 自动实体关系映射到知识图谱
    - 无需手动管理记忆ID

    Args:
        text: 要添加的文本内容
        user_id: 用户ID（可选）
        **kwargs: 其他参数（如agent_id, run_id, metadata等）

    Returns:
        包含操作结果和实体提取结果的字典
    """
    try:
        memory = await get_memory_client()
        if memory is None:
            return {
                "status": "error",
                "message": "AsyncMemory客户端未初始化",
                "id": None
            }

        logger.info(f"智能添加记忆: {text[:50]}... (用户: {user_id})")

        # 🔥 智能检测：检查是否存在相似记忆
        similar_memories = await _check_similar_memories(text, memory, user_id or "default_user")

        if similar_memories:
            # 发现相似记忆，尝试智能更新
            most_similar = similar_memories[0]
            similarity_score = most_similar.get('score', 0)
            comprehensive_score = most_similar.get('comprehensive_score', similarity_score)
            memory_id = most_similar.get('id')

            logger.info(f"发现相似记忆 (相似度: {similarity_score:.3f}, 综合得分: {comprehensive_score:.3f})，执行智能更新: {memory_id}")

            # 直接调用 mem0 的更新逻辑
            try:
                memory_result = await memory.update(memory_id, text)

                # 🔧 智能更新：只提取增量实体，避免重复
                entities = []
                relations = []
                try:
                    # 获取原记忆内容进行对比
                    original_memory = most_similar.get('memory', '')

                    # 计算文本差异，只对新增内容进行实体提取
                    if len(text) > len(original_memory) * 1.2:  # 新文本明显更长，可能有新实体
                        import sys
                        import os
                        sys.path.insert(0, os.path.dirname(__file__))
                        from dynamic_entity_manager import DynamicEntityManager
                        entity_manager = DynamicEntityManager(memory)

                        # 只提取新增部分的实体
                        new_content = text[len(original_memory)//2:]  # 提取后半部分作为新内容
                        episode = await entity_manager.process_episode(new_content, user_id or "default_user")
                        entities = episode.entities
                        relations = episode.relations
                        logger.info(f"智能更新：提取增量实体 {len(entities)} 个，关系 {len(relations)} 个")
                    else:
                        logger.info("智能更新：内容相似度高，跳过实体提取避免重复")

                except Exception as entity_error:
                    logger.warning(f"智能更新实体提取失败: {entity_error}")

                logger.info(f"智能更新成功: {memory_id}")
                return {
                    "status": "success",
                    "message": f"智能更新相似记忆 (综合得分: {comprehensive_score:.3f})",
                    "id": memory_id,
                    "operation": "update",
                    "similarity_score": similarity_score,
                    "comprehensive_score": comprehensive_score,
                    "memory_result": memory_result,
                    "entities_count": len(entities),
                    "relations_count": len(relations)
                }
            except Exception as update_error:
                logger.warning(f"智能更新失败，转为新增记忆: {update_error}")

        # 🔧 修复长文本处理：调整执行顺序
        entities = []
        relations = []
        episode_id = None
        merge_results = {"merged_entities": 0, "merged_relations": 0}

        try:
            import sys
            import os
            sys.path.insert(0, os.path.dirname(__file__))
            from dynamic_entity_manager import DynamicEntityManager
            entity_manager = DynamicEntityManager(memory)

            # 🔧 长记忆优化：根据文本长度选择处理策略
            if len(text) > 200:
                logger.info(f"检测到长记忆({len(text)}字)，先进行全局实体提取")
                # 🔥 关键修复：在mem0分割之前进行全局实体提取
                episode = await entity_manager.process_episode_global(text, user_id or "default_user")

                entities = episode.entities
                relations = episode.relations
                episode_id = episode.episode_id

                logger.info(f"全局实体提取完成: {len(entities)} 个实体, {len(relations)} 个关系")

                # 🔥 自动知识图谱合并
                merge_results = await _auto_merge_knowledge_graph(episode, memory, user_id or "default_user")
                logger.info(f"知识图谱合并完成: {merge_results}")

                # 现在添加记忆到向量存储（mem0会自动分割，但图关系已经建立）
                memory_result = await memory.add(text, user_id=user_id, **kwargs)

            else:
                logger.info(f"短记忆({len(text)}字)，使用标准处理")
                # 短记忆：先添加记忆，再提取实体
                memory_result = await memory.add(text, user_id=user_id, **kwargs)

                # 然后进行实体提取
                episode = await entity_manager.process_episode(text, user_id or "default_user")

                entities = episode.entities
                relations = episode.relations
                episode_id = episode.episode_id

                logger.info(f"标准实体提取完成: {len(entities)} 个实体, {len(relations)} 个关系")

                # 自动知识图谱合并
                merge_results = await _auto_merge_knowledge_graph(episode, memory, user_id or "default_user")

        except Exception as entity_error:
            logger.warning(f"实体提取失败: {entity_error}")
            # 如果实体提取失败，至少要添加记忆
            if 'memory_result' not in locals():
                memory_result = await memory.add(text, user_id=user_id, **kwargs)
                merge_results = {"merged_entities": 0, "merged_relations": 0}

        logger.info(f"记忆添加成功: {memory_result}")

        return {
            "status": "success",
            "message": "新记忆添加成功，已自动合并知识图谱",
            "id": memory_result.get("id") if isinstance(memory_result, dict) else None,
            "operation": "create",
            "memory_result": memory_result,
            "episode_id": episode_id,
            "entities_count": len(entities),
            "relations_count": len(relations),
            "merge_results": merge_results if 'merge_results' in locals() else {"merged_entities": 0, "merged_relations": 0},
            "entities": [
                {
                    "name": entity.name,
                    "type": entity.entity_type,  # 动态字符串类型
                    "confidence": entity.confidence
                } for entity in entities[:5]  # 只显示前5个实体
            ],
            "relations": [
                {
                    "source": relation.source_entity,
                    "target": relation.target_entity,
                    "type": relation.relation_type,  # 动态字符串类型
                    "confidence": relation.confidence
                } for relation in relations[:5]  # 只显示前5个关系
            ]
        }

    except Exception as e:
        logger.error(f"添加记忆时发生错误: {e}")
        return {
            "status": "error",
            "message": f"添加记忆失败: {str(e)}",
            "id": None
        }


async def search_memory(query: str, user_id: Optional[str] = None, limit: int = 5, **kwargs) -> Dict[str, Any]:
    """
    搜索相关记忆

    Args:
        query: 搜索查询文本
        user_id: 用户ID（可选）
        limit: 返回结果数量限制
        **kwargs: 其他参数（如agent_id, run_id, filters等）

    Returns:
        包含搜索结果的字典
    """
    try:
        memory = await get_memory_client()
        if memory is None:
            return {
                "status": "error",
                "message": "AsyncMemory客户端未初始化",
                "results": []
            }

        logger.info(f"混合搜索记忆: {query} (用户: {user_id}, 限制: {limit})")

        # 🔥 使用混合搜索（向量 + 图搜索）
        try:
            import sys
            import os
            sys.path.insert(0, os.path.dirname(__file__))
            from dynamic_entity_manager import DynamicEntityManager
            entity_manager = DynamicEntityManager(memory)

            # 执行混合搜索（内部获取更多结果，格式化时再限制显示）
            # 搜索50条 + 推荐15条 + 缓冲区 = 100条
            internal_limit = max(100, limit * 2)  # 确保有足够的结果用于推荐
            hybrid_result = await entity_manager.hybrid_search(query, user_id or "default_user", internal_limit)

            if hybrid_result.get('status') == 'success':
                results = hybrid_result.get('results', [])
                vector_count = hybrid_result.get('vector_count', 0)
                graph_count = hybrid_result.get('graph_count', 0)
                total_count = hybrid_result.get('total_count', 0)

                # 🔥 为混合搜索添加推荐系统
                # 分离搜索结果和生成推荐
                search_results = [r for r in results if r.get('source') in ['vector', 'graph']]

                # 🔥 生成智能推荐（使用现有的智能推荐系统）
                # 基于搜索结果生成相关推荐
                recommendation_result = await get_intelligent_recommendations(
                    user_id=user_id or "default_user",
                    recommendation_type="real_time",
                    context={"current_query": query},
                    limit=20  # 获取更多推荐供后续筛选
                )

                # 转换推荐格式以匹配搜索结果格式
                recommendations = []
                if recommendation_result.get('status') == 'success':
                    raw_recommendations = recommendation_result.get('recommendations', [])
                    for i, rec in enumerate(raw_recommendations):
                        recommendations.append({
                            'id': f"rec_{i}_{hash(rec.get('content', '')) % 10000}",
                            'memory': rec.get('content', ''),
                            'source': 'recommendation',
                            'score': rec.get('confidence', 0.5),
                            'recommendation_type': rec.get('type', 'contextual'),
                            'reason': rec.get('reason', '智能推荐')
                        })

                # 合并搜索结果和推荐
                all_results = search_results + recommendations

                # 🔧 根据用户请求的limit调整结果数量
                # 默认：搜索50条 + 推荐15条 = 65条
                if limit >= 65 or limit == 10:  # 10是默认值，实际想要更多
                    search_limit = min(len(search_results), 50)  # 搜索结果最多50条
                    recommendation_limit = min(len(recommendations), 15)  # 推荐最多15条
                    final_results = search_results[:search_limit] + recommendations[:recommendation_limit]
                    display_limit = len(final_results)
                else:
                    final_results = all_results[:limit]
                    display_limit = limit

                logger.info(f"混合搜索完成: 向量 {vector_count} 条, 图 {graph_count} 条, 显示 {len(final_results)}/{total_count} 条")
                return {
                    "status": "success",
                    "message": f"混合搜索完成，显示 {len(final_results)} 条结果",
                    "results": final_results,
                    "vector_count": vector_count,
                    "graph_count": graph_count,
                    "total_count": len(final_results),
                    "original_total": total_count
                }
            else:
                logger.warning(f"混合搜索失败，回退到基础搜索: {hybrid_result.get('message')}")

        except Exception as hybrid_error:
            logger.warning(f"混合搜索失败，回退到基础搜索: {hybrid_error}")

        # 回退到基础搜索
        search_response = await memory.search(query, user_id=user_id, limit=limit, **kwargs)

        # 提取实际的搜索结果
        if isinstance(search_response, dict):
            results = search_response.get('results', [])
            relations = search_response.get('relations', [])
        else:
            results = search_response if isinstance(search_response, list) else []
            relations = []

        logger.info(f"基础搜索完成，找到 {len(results)} 条结果")
        return {
            "status": "success",
            "message": f"搜索完成，找到 {len(results)} 条结果",
            "results": results,
            "relations": relations,
            "vector_count": len(results),
            "graph_count": 0,
            "total_count": len(results)
        }
        
    except Exception as e:
        logger.error(f"搜索记忆时发生错误: {e}")
        return {
            "status": "error",
            "message": f"搜索失败: {str(e)}",
            "results": []
        }


async def get_all_memories(user_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    获取所有记忆

    Args:
        user_id: 用户ID（可选）
        **kwargs: 其他参数

    Returns:
        包含所有记忆的字典
    """
    try:
        memory = await get_memory_client()
        if memory is None:
            return {
                "status": "error",
                "message": "AsyncMemory客户端未初始化",
                "memories": []
            }

        logger.info(f"获取所有记忆 (用户: {user_id})")

        # 调用AsyncMemory的get_all方法
        memories = await memory.get_all(user_id=user_id, **kwargs)

        # 计算记忆数量
        memory_count = 0
        if isinstance(memories, dict):
            results = memories.get('results', [])
            relations = memories.get('relations', [])
            memory_count = len(results) + len(relations)
        elif isinstance(memories, list):
            memory_count = len(memories)

        logger.info(f"获取完成，共 {memory_count} 条记忆")

        # 格式化返回的记忆数据
        formatted_memories = []
        if isinstance(memories, dict):
            results = memories.get('results', [])
            for memory in results:
                if isinstance(memory, dict):
                    formatted_memories.append(memory)
                else:
                    # 如果是字符串，转换为字典格式
                    formatted_memories.append({"memory": str(memory)})
        elif isinstance(memories, list):
            for memory in memories:
                if isinstance(memory, dict):
                    formatted_memories.append(memory)
                else:
                    # 如果是字符串，转换为字典格式
                    formatted_memories.append({"memory": str(memory)})

        return {
            "status": "success",
            "message": f"获取完成，共 {memory_count} 条记忆",
            "memories": formatted_memories
        }
        
    except Exception as e:
        logger.error(f"获取记忆时发生错误: {e}")
        return {
            "status": "error",
            "message": f"获取记忆失败: {str(e)}",
            "memories": []
        }


async def delete_all_memories(user_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    删除用户的所有记忆

    Args:
        user_id: 用户ID（可选）
        **kwargs: 其他参数

    Returns:
        删除结果的字典
    """
    try:
        memory = await get_memory_client()
        if memory is None:
            return {
                "status": "error",
                "message": "AsyncMemory客户端未初始化"
            }

        # 如果没有提供 user_id，使用默认值
        if user_id is None:
            user_id = "default_user"

        logger.info(f"开始删除用户 {user_id} 的所有记忆")

        # 删除所有记忆
        result = await memory.delete_all(user_id=user_id)

        logger.info(f"删除完成，结果: {result}")

        return {
            "status": "success",
            "message": "所有记忆已删除",
            "result": result
        }

    except Exception as e:
        logger.error(f"删除所有记忆失败: {e}")
        return {
            "status": "error",
            "message": f"删除记忆失败: {str(e)}"
        }


async def get_memory(memory_id: str, user_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    获取单个记忆的详细信息

    Args:
        memory_id: 记忆的唯一标识符
        user_id: 用户ID（可选）
        **kwargs: 其他参数

    Returns:
        包含记忆详情的字典
    """
    try:
        memory = await get_memory_client()
        if memory is None:
            return {
                "status": "error",
                "message": "AsyncMemory客户端未初始化",
                "memory": None
            }

        logger.info(f"获取记忆: {memory_id} (用户: {user_id})")

        # 调用AsyncMemory的get方法
        result = await memory.get(memory_id)

        if result:
            logger.info(f"记忆获取成功: {memory_id}")
            return {
                "status": "success",
                "message": "记忆获取成功",
                "memory": result
            }
        else:
            return {
                "status": "error",
                "message": f"未找到记忆: {memory_id}",
                "memory": None
            }

    except Exception as e:
        logger.error(f"获取记忆失败: {e}")
        return {
            "status": "error",
            "message": f"获取记忆失败: {str(e)}",
            "memory": None
        }




async def process_episode_with_entities(content: str, user_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    处理情节并提取实体关系（基于Graphiti的情节式处理）

    Args:
        content: 情节内容
        user_id: 用户ID（可选）
        **kwargs: 其他参数

    Returns:
        包含情节处理结果的字典
    """
    try:
        memory = await get_memory_client()
        if memory is None:
            return {
                "status": "error",
                "message": "AsyncMemory客户端未初始化",
                "episode": None
            }

        logger.info(f"处理情节: {content[:50]}... (用户: {user_id})")

        # 使用增强的实体管理器
        from enhanced_entity_manager import EnhancedEntityManager
        entity_manager = EnhancedEntityManager(memory)

        # 处理情节
        episode = await entity_manager.process_episode(content, user_id or "default_user")

        logger.info(f"情节处理完成: {episode.episode_id}")

        return {
            "status": "success",
            "message": "情节处理完成",
            "episode": {
                "id": episode.episode_id,
                "content": episode.content,
                "entities_count": len(episode.entities),
                "relations_count": len(episode.relations),
                "entities": [
                    {
                        "name": entity.name,
                        "type": entity.entity_type.value,
                        "confidence": entity.confidence
                    } for entity in episode.entities
                ],
                "relations": [
                    {
                        "source": relation.source_entity,
                        "target": relation.target_entity,
                        "type": relation.relation_type.value,
                        "confidence": relation.confidence
                    } for relation in episode.relations
                ]
            }
        }

    except Exception as e:
        logger.error(f"情节处理失败: {e}")
        return {
            "status": "error",
            "message": f"情节处理失败: {str(e)}",
            "episode": None
        }


async def extract_entities_from_memory(memory_text: str, user_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    从记忆文本中提取实体和关系（基于mem0的实现）

    Args:
        memory_text: 记忆文本
        user_id: 用户ID（可选）
        **kwargs: 其他参数

    Returns:
        包含提取的实体和关系的字典
    """
    try:
        memory = await get_memory_client()
        if memory is None:
            return {
                "status": "error",
                "message": "AsyncMemory客户端未初始化",
                "entities": [],
                "relations": []
            }

        logger.info(f"从文本提取实体: {memory_text[:50]}... (用户: {user_id})")

        # 使用mem0的图数据库进行实体提取
        if hasattr(memory, 'graph') and memory.graph:
            # 构建过滤器
            filters = {'user_id': user_id} if user_id else {}

            # 调用图数据库的add方法来触发实体提取
            graph_result = memory.graph.add(memory_text, filters)

            logger.info(f"实体提取完成: {graph_result}")

            return {
                "status": "success",
                "message": "实体提取完成",
                "entities": graph_result.get("added_entities", []),
                "deleted_entities": graph_result.get("deleted_entities", []),
                "graph_result": graph_result
            }
        else:
            return {
                "status": "error",
                "message": "图数据库未启用",
                "entities": [],
                "relations": []
            }

    except Exception as e:
        logger.error(f"实体提取失败: {e}")
        return {
            "status": "error",
            "message": f"实体提取失败: {str(e)}",
            "entities": [],
            "relations": []
        }


async def get_entity_relations(entity_name: str, user_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    获取指定实体的关系网络

    Args:
        entity_name: 实体名称（如人名、地点、组织等）
        user_id: 用户ID（可选）
        **kwargs: 其他参数

    Returns:
        包含实体关系网络的字典
    """
    try:
        memory = await get_memory_client()
        if memory is None:
            return {
                "status": "error",
                "message": "AsyncMemory客户端未初始化",
                "relations": {}
            }

        logger.info(f"获取实体关系: {entity_name} (用户: {user_id})")

        # 检查是否启用了图数据库
        if not hasattr(memory, 'graph') or not memory.graph:
            return {
                "status": "error",
                "message": "图数据库未启用",
                "relations": {}
            }

        # 使用动态混合搜索
        import sys
        import os
        sys.path.insert(0, os.path.dirname(__file__))
        from dynamic_entity_manager import DynamicEntityManager
        entity_manager = DynamicEntityManager(memory)

        # 执行混合搜索（向量 + 图）
        hybrid_result = await entity_manager.hybrid_search(entity_name, user_id or "default_user", limit=20)

        if hybrid_result.get('status') == 'success':
            related_memories = hybrid_result.get('results', [])
        else:
            # 回退到传统搜索
            search_result = await memory.search(entity_name, user_id=user_id, limit=50)
            all_memories = search_result.get('results', []) if isinstance(search_result, dict) else []

            # 过滤相关记忆
            related_memories = []
            for mem in all_memories:
                memory_text = mem.get('memory', '').lower()
                entity_lower = entity_name.lower()
                score = mem.get('score', 0)

                if (entity_lower in memory_text or score > 0.5):
                    related_memories.append(mem)

            related_memories.sort(key=lambda x: x.get('score', 0), reverse=True)

        # 使用mem0的图数据库API获取实体关系
        try:
            # 构建过滤器
            filters = {'user_id': user_id} if user_id else {}

            # 搜索图数据库中的实体关系
            graph_search_results = memory.graph.search(entity_name, filters=filters, limit=20)
            all_graph_data = memory.graph.get_all(filters=filters, limit=50)

            graph_relations = {
                "search_results": graph_search_results,
                "all_entities": all_graph_data,
                "entity_count": len(all_graph_data),
                "search_count": len(graph_search_results)
            }

        except Exception as graph_error:
            logger.warning(f"图数据库查询失败: {graph_error}")
            graph_relations = {
                "search_results": [],
                "all_entities": [],
                "entity_count": 0,
                "search_count": 0
            }

        # 🔥 生成智能推荐（传入已有记忆用于去重）
        try:
            recommendations = await _generate_contextual_recommendations(
                entity_name, memory, user_id or "default_user", graph_search_results, related_memories
            )
        except Exception as rec_error:
            logger.warning(f"推荐生成失败: {rec_error}")
            recommendations = {
                "related_entities": [],
                "suggested_queries": [],
                "contextual_insights": [],
                "next_actions": []
            }

        # 构建关系网络信息
        relations = {
            "entity": entity_name,
            "related_memories": [
                {
                    "id": mem.get("id"),
                    "memory": mem.get("memory"),
                    "score": mem.get("score", 0)
                }
                for mem in related_memories  # 返回所有相关记忆
            ],
            "graph_relations": graph_relations,
            "memory_count": len(related_memories),
            # 🔥 新增推荐信息
            "recommendations": recommendations
        }

        logger.info(f"实体关系获取完成: {entity_name}, 相关记忆: {len(related_memories)}")

        return {
            "status": "success",
            "message": f"实体 '{entity_name}' 的关系网络获取完成",
            "relations": relations
        }

    except Exception as e:
        logger.error(f"获取实体关系失败: {e}")
        return {
            "status": "error",
            "message": f"获取实体关系失败: {str(e)}",
            "relations": {}
        }


async def get_intelligent_recommendations(
    user_id: Optional[str] = None,
    recommendation_type: str = "personalized",
    context: Dict = None,
    limit: int = 10
) -> Dict[str, Any]:
    """
    获取智能推荐

    Args:
        user_id: 用户ID
        recommendation_type: 推荐类型 (personalized, discovery, real_time)
        context: 上下文信息
        limit: 推荐数量限制

    Returns:
        包含推荐结果的字典
    """
    try:
        memory = await get_memory_client()
        if memory is None:
            return {
                "status": "error",
                "message": "AsyncMemory客户端未初始化",
                "recommendations": []
            }

        logger.info(f"生成智能推荐: 类型={recommendation_type}, 用户={user_id}, 限制={limit}")

        if context is None:
            context = {}

        recommendations = []

        if recommendation_type == "personalized":
            # 个性化推荐：基于用户的记忆历史
            all_memories_result = await get_all_memories(user_id=user_id)
            if all_memories_result.get('status') == 'success':
                memories = all_memories_result.get('memories', [])

                # 分析用户兴趣 - 根据用户请求的limit调整数量
                recent_count = min(limit, len(memories), 50)  # 最多50条
                recent_memories = memories[-recent_count:] if memories else []
                for mem in recent_memories:
                    recommendations.append({
                        'content': mem.get('memory', ''),
                        'type': 'recent_activity',
                        'confidence': 0.8,
                        'reason': '基于最近活动'
                    })

        elif recommendation_type == "discovery":
            # 探索推荐：发现新的相关内容
            all_memories_result = await get_all_memories(user_id=user_id)
            if all_memories_result.get('status') == 'success':
                memories = all_memories_result.get('memories', [])

                # 随机推荐一些记忆供探索
                import random
                if memories:
                    sample_size = min(limit, len(memories))
                    sampled = random.sample(memories, sample_size)
                    for mem in sampled:
                        recommendations.append({
                            'content': mem.get('memory', ''),
                            'type': 'discovery',
                            'confidence': 0.6,
                            'reason': '探索发现'
                        })

        elif recommendation_type == "real_time":
            # 🔥 轻量级实时推荐 - 无需语义扩展和预训练模型
            current_query = context.get("current_query", "")
            if current_query:
                try:
                    # 🔥 方法1：基于关键词频率的推荐
                    keyword_recommendations = await _get_keyword_based_recommendations(
                        current_query, memory, user_id, limit=5
                    )
                    recommendations.extend(keyword_recommendations)

                    # 🔥 方法2：基于用户最近活动的推荐
                    recent_activity_recommendations = await _get_recent_activity_recommendations(
                        memory, user_id, current_query, limit=3
                    )
                    recommendations.extend(recent_activity_recommendations)

                    # 🔥 方法3：基于共现模式的推荐
                    cooccurrence_recommendations = await _get_cooccurrence_recommendations(
                        current_query, memory, user_id, limit=3
                    )
                    recommendations.extend(cooccurrence_recommendations)

                except Exception as e:
                    logger.warning(f"实时推荐生成失败: {e}")
                    # 降级：提供基础推荐
                    recommendations.append({
                        'content': f'基于查询"{current_query}"的相关推荐暂时不可用',
                        'type': 'fallback',
                        'confidence': 0.3,
                        'reason': '系统降级推荐'
                    })

        # 🔥 高价值过滤：只推荐置信度 > 0.6 的内容
        HIGH_VALUE_CONFIDENCE = 0.6
        high_value_recommendations = [
            rec for rec in recommendations
            if rec.get('confidence', 0) >= HIGH_VALUE_CONFIDENCE
        ]

        # 如果高价值推荐太少，降低阈值到0.5
        if len(high_value_recommendations) < limit // 2:
            HIGH_VALUE_CONFIDENCE = 0.5
            high_value_recommendations = [
                rec for rec in recommendations
                if rec.get('confidence', 0) >= HIGH_VALUE_CONFIDENCE
            ]

        final_recommendations = high_value_recommendations[:limit]

        return {
            "status": "success",
            "message": f"生成了 {len(final_recommendations)} 条高价值推荐",
            "recommendations": final_recommendations,
            "type": recommendation_type,
            "count": len(final_recommendations),
            "quality_threshold": HIGH_VALUE_CONFIDENCE
        }

    except Exception as e:
        logger.error(f"智能推荐失败: {e}")
        return {
            "status": "error",
            "message": f"智能推荐失败: {str(e)}",
            "recommendations": []
        }


async def _generate_contextual_recommendations(
    entity_name: str,
    memory,
    user_id: str,
    graph_results: List[Dict[str, Any]],
    existing_memories: List[Dict[str, Any]] = None  # 🔥 新增：已有的搜索结果，用于去重
) -> Dict[str, Any]:
    """
    🔥 生成上下文感知的智能推荐 - 集成版
    包含：异常检测、智能知识补全、个性化推荐（前3个）
    """
    try:
        recommendations = {
            "related_entities": [],
            "suggested_queries": [],
            "contextual_insights": [],
            "next_actions": []
        }

        # 1. 🔍 异常检测与质量控制
        quality_issues = await _detect_entity_quality_issues(entity_name, memory, user_id)

        # 2. 🧩 智能知识补全 - 预测缺失关系
        predicted_relations = await _predict_missing_relations(entity_name, memory, user_id, graph_results)

        # 🔥 收集已有搜索结果中的实体，用于去重
        existing_entities = set()
        if existing_memories:
            for memory in existing_memories:
                memory_text = memory.get('memory', '').lower()
                # 简单提取可能的实体名称（包含大写字母开头的词）
                import re
                entities_in_memory = re.findall(r'\b[A-Z][a-zA-Z]+', memory.get('memory', ''))
                existing_entities.update(entities_in_memory)

        # 3. 🤝 个性化推荐整合 - 基于图关系的实体推荐（去重后保留前10个）
        related_entities = set()
        entity_scores = {}

        for result in graph_results[:20]:  # 增加处理数量
            source = result.get("source")
            destination = result.get("destination")
            relationship = result.get("relationship")

            # 计算实体重要性分数，并排除已存在的实体
            if source and source != entity_name and source not in existing_entities:
                score = _calculate_entity_importance_score(source, relationship)
                entity_scores[source] = max(entity_scores.get(source, 0), score)
                related_entities.add(source)

            if destination and destination != entity_name and destination not in existing_entities:
                score = _calculate_entity_importance_score(destination, relationship)
                entity_scores[destination] = max(entity_scores.get(destination, 0), score)
                related_entities.add(destination)

        # 🔥 高价值过滤：提高质量控制标准
        HIGH_VALUE_THRESHOLD = 0.75  # 提高阈值从0.7到0.75
        high_value_entities = {k: v for k, v in entity_scores.items() if v >= HIGH_VALUE_THRESHOLD}

        # 🔧 额外质量过滤：排除低质量实体
        filtered_entities = {}
        for entity, score in high_value_entities.items():
            # 过滤条件：
            # 1. 实体名称长度合理（2-50字符）
            # 2. 不是纯数字或特殊字符
            # 3. 不是常见的无意义词汇
            if (2 <= len(entity) <= 50 and
                not entity.isdigit() and
                not all(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in entity) and
                entity.lower() not in ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']):
                filtered_entities[entity] = score

        # 按重要性排序，保留前8个高价值实体推荐（减少数量提高质量）
        top_entities = sorted(filtered_entities.items(), key=lambda x: x[1], reverse=True)[:8]

        for entity, score in top_entities:
            recommendations["related_entities"].append({
                "entity": entity,
                "reason": "高价值关系发现",
                "confidence": min(score, 1.0),
                "importance_score": score,
                "quality_score": _calculate_entity_quality_score(entity, existing_memories)  # 新增质量评分
            })

        # 4. 🔥 生成建议查询（前10个，通用场合）
        base_queries = []

        # 基于top实体生成查询
        for entity, score in top_entities[:5]:  # 前5个实体
            base_queries.append(f"{entity_name}和{entity}的关系")

        # 添加通用查询（适用于任何场合）
        base_queries.extend([
            f"{entity_name}的详细信息",
            f"{entity_name}的相关内容",
            f"与{entity_name}关联的信息",
            f"{entity_name}的背景资料"
        ])

        # 如果有预测关系，添加预测查询
        if predicted_relations:
            for pred in predicted_relations[:2]:  # 前2个预测
                base_queries.append(f"{entity_name}可能还有{pred}相关信息")

        recommendations["suggested_queries"] = base_queries[:10]  # 🔥 保留前10个

        # 5. 上下文洞察（包含质量检测结果）
        insights = []
        if graph_results:
            relationship_types = [r.get("relationship") for r in graph_results if r.get("relationship")]
            unique_relationships = list(set(relationship_types))[:3]
            insights.append(f"{entity_name}主要通过{', '.join(unique_relationships)}等关系与其他实体相连")

        if quality_issues:
            insights.append(f"检测到{len(quality_issues)}个数据质量问题，建议优化")

        if predicted_relations:
            insights.append(f"预测{entity_name}可能还具备{len(predicted_relations)}个未记录的关系")

        recommendations["contextual_insights"] = insights[:10]  # 🔥 保留前10个

        # 6. 建议的下一步操作（前10个）
        next_actions = [
            f"探索{entity_name}的详细信息",
            f"分析{entity_name}的影响力网络",
            f"查看{entity_name}的时间线",
            f"搜索{entity_name}的相关项目",
            f"了解{entity_name}的技能发展",
            f"查找{entity_name}的协作伙伴",
            f"分析{entity_name}的专业领域",
            f"探索{entity_name}的学习路径"
        ]

        if quality_issues:
            next_actions.append(f"修复{entity_name}的数据质量问题")
            next_actions.append(f"优化{entity_name}的信息结构")

        recommendations["next_actions"] = next_actions[:10]  # 🔥 保留前10个

        return recommendations

    except Exception as e:
        logger.warning(f"生成推荐失败: {e}")
        return {
            "related_entities": [],
            "suggested_queries": [],
            "contextual_insights": [],
            "next_actions": []
        }


def _calculate_entity_importance_score(entity: str, relationship: str) -> float:
    """计算实体重要性分数"""
    # 不同关系类型的重要性权重
    relationship_weights = {
        'IS_A': 0.9,
        'WORKS_FOR': 0.8,
        'EXPERT_IN': 0.8,
        'USES': 0.7,
        'LOCATED_IN': 0.6,
        'RELATES_TO': 0.5
    }

    base_score = relationship_weights.get(relationship.upper(), 0.5)

    # 根据实体名称长度调整（更具体的实体可能更重要）
    length_factor = min(len(entity) / 20, 1.0)

    return base_score * (0.7 + 0.3 * length_factor)


async def _detect_entity_quality_issues(entity_name: str, memory, user_id: str) -> List[str]:
    """检测特定实体的质量问题"""
    issues = []

    try:
        if hasattr(memory, 'graph') and memory.graph:
            # 检查是否有重复的相似实体
            similar_query = f"""
            MATCH (n:__Entity__ {{user_id: $user_id}})
            WHERE toLower(n.name) CONTAINS toLower($entity_name)
              AND n.name <> $entity_name
            RETURN n.name as similar_name
            LIMIT 3
            """

            similar_entities = memory.graph.graph.query(
                similar_query,
                params={"user_id": user_id, "entity_name": entity_name}
            )

            if similar_entities:
                issues.append(f"发现相似实体: {[s.get('similar_name') for s in similar_entities]}")

    except Exception as e:
        logger.warning(f"质量检测失败: {e}")

    return issues


async def _predict_missing_relations(entity_name: str, memory, user_id: str, existing_relations: List[Dict]) -> List[str]:
    """智能知识补全 - 预测可能缺失的关系"""
    predicted = []

    try:
        if hasattr(memory, 'graph') and memory.graph:
            # 基于相似实体的关系模式预测
            existing_targets = {r.get("destination") for r in existing_relations if r.get("source") == entity_name}

            # 查找具有相似关系模式的实体
            pattern_query = f"""
            MATCH (similar:__Entity__ {{user_id: $user_id}})-[r]->(target:__Entity__ {{user_id: $user_id}})
            WHERE similar.name <> $entity_name
              AND target.name IN $existing_targets
            WITH similar, collect(DISTINCT target.name) as common_targets
            WHERE size(common_targets) >= 2
            MATCH (similar)-[r2]->(potential:__Entity__ {{user_id: $user_id}})
            WHERE NOT potential.name IN $existing_targets
              AND NOT potential.name = $entity_name
            RETURN DISTINCT potential.name as predicted_target, type(r2) as predicted_relation
            LIMIT 3
            """

            predictions = memory.graph.graph.query(
                pattern_query,
                params={
                    "user_id": user_id,
                    "entity_name": entity_name,
                    "existing_targets": list(existing_targets)
                }
            )

            for pred in predictions:
                target = pred.get("predicted_target")
                relation = pred.get("predicted_relation")
                if target and relation:
                    predicted.append(f"{relation} {target}")

    except Exception as e:
        logger.warning(f"关系预测失败: {e}")

    return predicted[:3]  # 只返回前3个预测


async def _check_similar_memories(text: str, memory, user_id: str) -> List[Dict]:
    """智能检查相似记忆（基于相似度、时间和重要性）"""
    try:
        # 使用向量搜索查找相似记忆
        search_result = await memory.search(text, user_id=user_id, limit=5)

        if isinstance(search_result, dict) and 'results' in search_result:
            # 计算综合得分并过滤
            enhanced_memories = []

            for result in search_result['results']:
                similarity_score = result.get('score', 0)

                # 基础相似度过滤（> 0.7）
                if similarity_score > 0.7:
                    # 计算综合得分
                    comprehensive_score = _calculate_comprehensive_similarity_score(result, text)

                    # 综合得分 > 0.75 才认为是相似记忆
                    if comprehensive_score > 0.75:
                        result['comprehensive_score'] = comprehensive_score
                        enhanced_memories.append(result)

            # 按综合得分排序
            enhanced_memories.sort(key=lambda x: x.get('comprehensive_score', 0), reverse=True)
            return enhanced_memories

        return []
    except Exception as e:
        logger.warning(f"检查相似记忆失败: {e}")
        return []


def _calculate_comprehensive_similarity_score(memory_result: Dict, new_text: str) -> float:
    """
    计算综合替换得分（得分越高，越适合被替换）

    因子说明：
    - 相似度：越高越适合替换（60%权重）
    - 时间：越久越适合替换（25%权重）
    - 重要性：越重要越不适合替换（15%权重）
    """
    try:
        import datetime
        from dateutil import parser

        # 1. 基础相似度得分 (权重: 60%)
        similarity_score = memory_result.get('score', 0)

        # 2. 时间替换因子 (权重: 25%) - 时间越久越容易被替换
        time_replacement_factor = 0.5  # 默认值
        try:
            # 尝试从记忆中提取时间信息
            created_at = memory_result.get('created_at')
            if created_at:
                if isinstance(created_at, str):
                    created_time = parser.parse(created_at)
                else:
                    created_time = created_at

                # 计算时间差（天数）
                now = datetime.datetime.now(datetime.timezone.utc)
                if created_time.tzinfo is None:
                    created_time = created_time.replace(tzinfo=datetime.timezone.utc)

                days_old = (now - created_time).days

                # 时间替换因子：时间越久，越容易被替换（得分越高）
                if days_old <= 7:
                    time_replacement_factor = 0.3  # 7天内的记忆不容易被替换
                elif days_old <= 30:
                    time_replacement_factor = 0.5  # 30天内中等
                elif days_old <= 90:
                    time_replacement_factor = 0.7 + (days_old - 30) * 0.005  # 30-90天逐渐增加
                else:
                    time_replacement_factor = 1.0  # 90天后很容易被替换

        except Exception:
            time_replacement_factor = 0.6  # 无法获取时间信息时的默认值

        # 3. 内容重要性因子 (权重: 15%) - 重要性越高，越不容易被替换
        importance_score = _calculate_content_importance(memory_result.get('memory', ''), new_text)
        importance_resistance_factor = 1.0 - importance_score  # 重要性越高，阻力越大

        # 综合得分计算：相似度 + 时间替换因子 + 重要性阻力
        # 得分越高，越适合被替换/更新
        comprehensive_score = (
            similarity_score * 0.6 +
            time_replacement_factor * 0.25 +
            importance_resistance_factor * 0.15
        )

        return min(comprehensive_score, 1.0)

    except Exception as e:
        logger.warning(f"计算综合相似度失败: {e}")
        return memory_result.get('score', 0) * 0.8  # 降级处理


def _calculate_content_importance(existing_content: str, new_content: str) -> float:
    """计算内容重要性因子"""
    try:
        # 重要性关键词（可扩展）
        important_keywords = {
            'high': ['工作', '项目', '技能', '经验', '学习', '专业', '重要', '关键'],
            'medium': ['喜欢', '兴趣', '习惯', '偏好', '想法', '计划'],
            'low': ['今天', '昨天', '刚才', '现在', '临时', '随便']
        }

        def get_content_score(content: str) -> float:
            content_lower = content.lower()
            score = 0.5  # 基础分数

            # 高重要性关键词
            for keyword in important_keywords['high']:
                if keyword in content_lower:
                    score += 0.15

            # 中等重要性关键词
            for keyword in important_keywords['medium']:
                if keyword in content_lower:
                    score += 0.1

            # 低重要性关键词（减分）
            for keyword in important_keywords['low']:
                if keyword in content_lower:
                    score -= 0.1

            # 内容长度因子（更详细的内容通常更重要）
            length_factor = min(len(content) / 100, 0.2)
            score += length_factor

            return min(max(score, 0.1), 1.0)

        # 计算两个内容的重要性并取平均值
        existing_score = get_content_score(existing_content)
        new_score = get_content_score(new_content)

        return (existing_score + new_score) / 2

    except Exception as e:
        logger.warning(f"计算内容重要性失败: {e}")
        return 0.5


async def _auto_merge_knowledge_graph(episode, memory, user_id: str) -> Dict[str, Any]:
    """
    🔧 自动知识图谱合并功能
    检测并合并重复实体，优化关系
    """
    merge_results = {
        "merged_entities": 0,
        "merged_relations": 0,
        "duplicate_entities_found": [],
        "optimization_actions": []
    }

    try:
        if not hasattr(memory, 'graph') or not memory.graph:
            return merge_results

        # 1. 检测当前添加的实体是否与现有实体重复
        for entity in episode.entities:
            similar_entities = await _find_similar_entities(entity.name, memory, user_id)

            if similar_entities:
                merge_results["duplicate_entities_found"].extend(similar_entities)
                merge_results["merged_entities"] += len(similar_entities)

                # 记录优化建议
                for similar in similar_entities:
                    merge_results["optimization_actions"].append(
                        f"建议合并 '{entity.name}' 和 '{similar['name']}'"
                    )

        # 2. 检测关系优化机会
        relation_optimizations = await _optimize_relations(episode.relations, memory, user_id)
        merge_results["merged_relations"] = len(relation_optimizations)
        merge_results["optimization_actions"].extend(relation_optimizations)

        # 3. 自动执行简单的合并操作（相似度很高的情况）
        auto_merged = await _perform_auto_merge(episode, memory, user_id)
        merge_results.update(auto_merged)

        return merge_results

    except Exception as e:
        logger.error(f"知识图谱合并失败: {e}")
        return merge_results


async def _find_similar_entities(entity_name: str, memory, user_id: str) -> List[Dict]:
    """查找相似实体"""
    similar_entities = []

    try:
        # 查找名称相似的实体
        similar_query = f"""
        MATCH (n:__Entity__ {{user_id: $user_id}})
        WHERE toLower(n.name) CONTAINS toLower($entity_name)
          OR toLower($entity_name) CONTAINS toLower(n.name)
          AND n.name <> $entity_name
          AND abs(size(n.name) - size($entity_name)) <= 3
        RETURN n.name as name, n.type as type,
               CASE
                 WHEN toLower(n.name) = toLower($entity_name) THEN 1.0
                 WHEN toLower(n.name) CONTAINS toLower($entity_name) THEN 0.8
                 WHEN toLower($entity_name) CONTAINS toLower(n.name) THEN 0.8
                 ELSE 0.6
               END as similarity
        ORDER BY similarity DESC
        LIMIT 5
        """

        results = memory.graph.graph.query(
            similar_query,
            params={"user_id": user_id, "entity_name": entity_name}
        )

        for result in results:
            if result.get("similarity", 0) > 0.7:  # 相似度阈值
                similar_entities.append({
                    "name": result.get("name"),
                    "type": result.get("type"),
                    "similarity": result.get("similarity")
                })

    except Exception as e:
        logger.warning(f"查找相似实体失败: {e}")

    return similar_entities


async def _optimize_relations(relations, memory, user_id: str) -> List[str]:
    """优化关系"""
    optimizations = []

    try:
        # 检测重复关系
        relation_pairs = set()
        for relation in relations:
            pair = (relation.source_entity, relation.target_entity, relation.relation_type)
            if pair in relation_pairs:
                optimizations.append(f"检测到重复关系: {pair[0]} -> {pair[1]} [{pair[2]}]")
            else:
                relation_pairs.add(pair)

    except Exception as e:
        logger.warning(f"关系优化失败: {e}")

    return optimizations


def _calculate_entity_quality_score(entity: str, existing_memories: List[Dict]) -> float:
    """
    计算实体质量评分 - 新增函数
    基于实体在现有记忆中的出现频率和上下文质量
    """
    try:
        if not existing_memories:
            return 0.5

        # 统计实体在记忆中的出现次数和上下文质量
        mention_count = 0
        context_quality_sum = 0.0

        for memory in existing_memories:
            memory_text = memory.get('memory', '').lower()
            entity_lower = entity.lower()

            if entity_lower in memory_text:
                mention_count += 1

                # 评估上下文质量
                context_quality = 0.5  # 基础质量

                # 如果实体周围有具体描述，提高质量分
                words = memory_text.split()
                entity_positions = [i for i, word in enumerate(words) if entity_lower in word]

                for pos in entity_positions:
                    # 检查前后词汇的质量
                    context_words = words[max(0, pos-3):pos+4]

                    # 高质量词汇
                    quality_keywords = ['专家', '负责', '开发', '设计', '管理', '领导', '创建', '实现',
                                      'expert', 'responsible', 'develop', 'design', 'manage', 'lead']

                    quality_boost = sum(0.1 for word in context_words if any(kw in word for kw in quality_keywords))
                    context_quality += min(quality_boost, 0.3)

                context_quality_sum += context_quality

        if mention_count == 0:
            return 0.3  # 未在记忆中出现，质量较低

        # 计算平均上下文质量
        avg_context_quality = context_quality_sum / mention_count

        # 频率权重（出现次数越多，质量越高，但有上限）
        frequency_score = min(mention_count * 0.1, 0.4)

        # 综合质量分：上下文质量 * 0.7 + 频率分 * 0.3
        quality_score = avg_context_quality * 0.7 + frequency_score * 0.3

        return min(quality_score, 1.0)

    except Exception as e:
        logger.warning(f"计算实体质量失败: {e}")
        return 0.5


async def _get_keyword_based_recommendations(
    query: str,
    memory,
    user_id: str,
    limit: int = 5
) -> List[Dict]:
    """
    基于关键词频率的推荐 - 使用TF-IDF思想，无需预训练模型
    """
    try:
        recommendations = []

        # 提取查询关键词
        query_keywords = _extract_keywords(query)

        if not query_keywords:
            return recommendations

        # 获取用户所有记忆
        all_memories_result = await get_all_memories(user_id=user_id)
        if all_memories_result.get('status') != 'success':
            return recommendations

        memories = all_memories_result.get('memories', [])
        if not memories:
            return recommendations

        # 计算每个记忆与查询的关键词匹配度
        scored_memories = []
        for memory in memories:
            memory_text = memory.get('memory', '').lower()
            memory_keywords = _extract_keywords(memory_text)

            # 计算关键词重叠度
            overlap_score = _calculate_keyword_overlap(query_keywords, memory_keywords)

            if overlap_score > 0.1:  # 最低相关度阈值
                scored_memories.append({
                    'memory': memory,
                    'score': overlap_score
                })

        # 按分数排序并选择前N个
        scored_memories.sort(key=lambda x: x['score'], reverse=True)

        for item in scored_memories[:limit]:
            recommendations.append({
                'content': item['memory'].get('memory', ''),
                'type': 'keyword_based',
                'confidence': min(item['score'], 1.0),
                'reason': '基于关键词匹配'
            })

        return recommendations

    except Exception as e:
        logger.warning(f"关键词推荐失败: {e}")
        return []


async def _get_recent_activity_recommendations(
    memory,
    user_id: str,
    current_query: str,
    limit: int = 3
) -> List[Dict]:
    """
    基于用户最近活动的推荐 - 时间权重推荐
    """
    try:
        recommendations = []

        # 获取用户最近的记忆（按时间排序）
        all_memories_result = await get_all_memories(user_id=user_id)
        if all_memories_result.get('status') != 'success':
            return recommendations

        memories = all_memories_result.get('memories', [])
        if not memories:
            return recommendations

        # 获取最近的记忆（最多20条）
        recent_memories = memories[-20:] if len(memories) > 20 else memories

        # 基于时间衰减计算推荐分数
        for i, memory in enumerate(reversed(recent_memories)):
            # 时间权重：越新的记忆权重越高
            time_weight = (i + 1) / len(recent_memories)

            # 简单的文本相关性检查
            memory_text = memory.get('memory', '').lower()
            query_lower = current_query.lower()

            # 检查是否有共同词汇
            memory_words = set(memory_text.split())
            query_words = set(query_lower.split())
            common_words = memory_words.intersection(query_words)

            if common_words:
                relevance_score = len(common_words) / max(len(query_words), 1)
                final_score = time_weight * 0.7 + relevance_score * 0.3

                recommendations.append({
                    'content': memory.get('memory', ''),
                    'type': 'recent_activity',
                    'confidence': min(final_score, 1.0),
                    'reason': '基于最近活动'
                })

                if len(recommendations) >= limit:
                    break

        return recommendations

    except Exception as e:
        logger.warning(f"最近活动推荐失败: {e}")
        return []


async def _get_cooccurrence_recommendations(
    query: str,
    memory,
    user_id: str,
    limit: int = 3
) -> List[Dict]:
    """
    基于词汇共现模式的推荐 - 简单的关联规则
    """
    try:
        recommendations = []

        # 获取用户所有记忆
        all_memories_result = await get_all_memories(user_id=user_id)
        if all_memories_result.get('status') != 'success':
            return recommendations

        memories = all_memories_result.get('memories', [])
        if not memories:
            return recommendations

        query_words = set(query.lower().split())

        # 查找包含查询词的记忆
        cooccurrence_memories = []
        for memory in memories:
            memory_text = memory.get('memory', '').lower()
            memory_words = set(memory_text.split())

            # 检查是否包含查询词
            if query_words.intersection(memory_words):
                # 计算额外的相关词汇
                additional_words = memory_words - query_words
                if additional_words:
                    cooccurrence_memories.append({
                        'memory': memory,
                        'additional_words': additional_words,
                        'overlap_count': len(query_words.intersection(memory_words))
                    })

        # 按重叠词数排序
        cooccurrence_memories.sort(key=lambda x: x['overlap_count'], reverse=True)

        for item in cooccurrence_memories[:limit]:
            confidence = min(item['overlap_count'] / len(query_words), 1.0)
            recommendations.append({
                'content': item['memory'].get('memory', ''),
                'type': 'cooccurrence',
                'confidence': confidence,
                'reason': '基于词汇共现'
            })

        return recommendations

    except Exception as e:
        logger.warning(f"共现推荐失败: {e}")
        return []


def _extract_keywords(text: str) -> List[str]:
    """
    简单的关键词提取 - 基于词频和长度，无需外部库
    """
    try:
        # 基础文本清理
        import re
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = text.split()

        # 过滤停用词（简单版本）
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            '的', '了', '在', '是', '我', '你', '他', '她', '它', '们', '这', '那'
        }

        # 提取关键词
        keywords = []
        for word in words:
            if (len(word) > 2 and
                word not in stop_words and
                not word.isdigit()):
                keywords.append(word)

        # 去重并返回
        return list(set(keywords))

    except Exception as e:
        logger.debug(f"关键词提取失败: {e}")
        return []


def _calculate_keyword_overlap(keywords1: List[str], keywords2: List[str]) -> float:
    """
    计算两个关键词列表的重叠度
    """
    try:
        if not keywords1 or not keywords2:
            return 0.0

        set1 = set(keywords1)
        set2 = set(keywords2)

        intersection = set1.intersection(set2)
        union = set1.union(set2)

        if not union:
            return 0.0

        # Jaccard相似度
        jaccard_similarity = len(intersection) / len(union)

        return jaccard_similarity

    except Exception as e:
        logger.debug(f"关键词重叠度计算失败: {e}")
        return 0.0


async def _perform_auto_merge(episode, memory, user_id: str) -> Dict[str, Any]:
    """执行自动合并"""
    auto_merge_results = {
        "auto_merged_entities": 0,
        "auto_merged_relations": 0
    }

    # 这里可以实现自动合并逻辑
    # 目前只是占位符，实际实现需要更复杂的逻辑

    return auto_merge_results


def check_connection_status() -> Dict[str, Any]:
    """
    检查各组件的连接状态
    
    Returns:
        包含连接状态信息的字典
    """
    status = {
        "memory_client": False,
        "vector_store": False,
        "llm": False,
        "graph_store": False,
        "embedder": False
    }
    
    try:
        memory = get_memory_client()
        if memory is not None:
            status["memory_client"] = True
            
            # 尝试检查各组件状态（简单测试）
            try:
                # 测试向量存储
                if hasattr(memory, 'vector_store') and memory.vector_store:
                    status["vector_store"] = True
            except:
                pass
                
            try:
                # 测试LLM
                if hasattr(memory, 'llm') and memory.llm:
                    status["llm"] = True
            except:
                pass
                
            try:
                # 测试图存储
                if hasattr(memory, 'graph') and memory.graph:
                    status["graph_store"] = True
            except:
                pass
                
            try:
                # 测试嵌入模型
                if hasattr(memory, 'embedding_model') and memory.embedding_model:
                    status["embedder"] = True
            except:
                pass
        
        return {
            "status": "success",
            "components": status,
            "message": "连接状态检查完成"
        }
        
    except Exception as e:
        logger.error(f"检查连接状态时发生错误: {e}")
        return {
            "status": "error",
            "components": status,
            "message": f"连接状态检查失败: {str(e)}"
        }


# 注意：使用 get_memory_client() 异步函数获取Memory实例


if __name__ == "__main__":
    # 测试代码
    print("=== mem0应用测试 ===")
    
    # 检查连接状态
    status = check_connection_status()
    print(f"连接状态: {status}")
    
    # 测试基本功能
    if status["components"]["memory_client"]:
        print("\n测试添加记忆...")
        result = add_memory("这是一个测试记忆", user_id="test_user")
        print(f"添加结果: {result}")
        
        print("\n测试搜索记忆...")
        search_result = search_memory("测试", user_id="test_user")
        print(f"搜索结果: {search_result}")
    else:
        print("Memory客户端未初始化，跳过功能测试")
