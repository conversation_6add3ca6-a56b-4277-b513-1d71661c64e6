#!/usr/bin/env python3
"""
图数据库诊断脚本
深度检查图数据库的状态和问题
"""

import asyncio
import sys
import os
from app import get_memory_client
from mem0_config import config
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


async def debug_graph_database():
    """深度诊断图数据库"""
    print("🔍 === 图数据库深度诊断 ===")
    
    try:
        # 1. 检查内存客户端
        print("\n1. 📋 检查内存客户端:")
        memory = await get_memory_client()
        if not memory:
            print("   ❌ 内存客户端初始化失败")
            return
        print("   ✅ 内存客户端初始化成功")
        
        # 2. 检查图数据库配置
        print("\n2. ⚙️ 检查图数据库配置:")
        print(f"   配置类型: {config.get('graph_store', {}).get('provider', 'None')}")
        print(f"   配置详情: {config.get('graph_store', {})}")
        
        # 3. 检查图数据库连接
        print("\n3. 🔗 检查图数据库连接:")
        if hasattr(memory, 'graph') and memory.graph:
            print("   ✅ 图数据库对象存在")
            print(f"   图数据库类型: {type(memory.graph)}")
            
            # 检查图数据库的具体属性
            if hasattr(memory.graph, 'graph'):
                print(f"   底层图对象: {type(memory.graph.graph)}")
                print(f"   节点标签: {getattr(memory.graph, 'node_label', 'Unknown')}")
            
        else:
            print("   ❌ 图数据库对象不存在")
            return
        
        # 4. 测试图数据库连接
        print("\n4. 🧪 测试图数据库连接:")
        try:
            # 尝试执行简单查询
            test_query = "MATCH (n) RETURN count(n) as total_nodes LIMIT 1"
            result = memory.graph.graph.query(test_query)
            print(f"   ✅ 图数据库连接正常，总节点数: {result[0].get('total_nodes', 0) if result else 0}")
        except Exception as e:
            print(f"   ❌ 图数据库连接失败: {e}")
            return
        
        # 5. 检查用户数据
        print("\n5. 👤 检查用户数据:")
        user_id = "test_user"
        try:
            # 查询用户的所有节点
            user_query = f"""
            MATCH (n:__Entity__ {{user_id: $user_id}})
            RETURN n.name as name, n.type as type, n.mentions as mentions
            ORDER BY n.mentions DESC
            """
            user_results = memory.graph.graph.query(user_query, params={"user_id": user_id})
            
            print(f"   用户 {user_id} 的实体数量: {len(user_results)}")
            for i, result in enumerate(user_results[:5]):  # 显示前5个
                print(f"     {i+1}. {result.get('name')} ({result.get('type')}) - 提及: {result.get('mentions', 0)}")
            
            # 查询用户的所有关系
            relation_query = f"""
            MATCH (a:__Entity__ {{user_id: $user_id}})-[r]->(b:__Entity__ {{user_id: $user_id}})
            RETURN a.name as source, type(r) as relationship, b.name as destination
            LIMIT 10
            """
            relation_results = memory.graph.graph.query(relation_query, params={"user_id": user_id})
            
            print(f"   用户 {user_id} 的关系数量: {len(relation_results)}")
            for i, result in enumerate(relation_results[:5]):  # 显示前5个
                print(f"     {i+1}. {result.get('source')} --[{result.get('relationship')}]--> {result.get('destination')}")
                
        except Exception as e:
            print(f"   ❌ 查询用户数据失败: {e}")
        
        # 6. 检查所有数据
        print("\n6. 🌐 检查所有数据:")
        try:
            # 查询所有节点
            all_nodes_query = "MATCH (n:__Entity__) RETURN count(n) as total"
            all_nodes_result = memory.graph.graph.query(all_nodes_query)
            total_nodes = all_nodes_result[0].get('total', 0) if all_nodes_result else 0
            
            # 查询所有关系
            all_relations_query = "MATCH ()-[r]->() RETURN count(r) as total"
            all_relations_result = memory.graph.graph.query(all_relations_query)
            total_relations = all_relations_result[0].get('total', 0) if all_relations_result else 0
            
            print(f"   数据库总节点数: {total_nodes}")
            print(f"   数据库总关系数: {total_relations}")
            
            # 查询所有用户
            users_query = "MATCH (n:__Entity__) RETURN DISTINCT n.user_id as user_id, count(n) as node_count"
            users_result = memory.graph.graph.query(users_query)
            
            print(f"   数据库中的用户:")
            for result in users_result:
                user_id_found = result.get('user_id')
                node_count = result.get('node_count', 0)
                print(f"     - {user_id_found}: {node_count} 个节点")
                
        except Exception as e:
            print(f"   ❌ 查询所有数据失败: {e}")
        
        # 7. 测试图搜索功能
        print("\n7. 🔍 测试图搜索功能:")
        try:
            filters = {'user_id': 'test_user'}
            
            # 测试搜索
            search_results = memory.graph.search("张三", filters=filters, limit=10)
            print(f"   搜索 '张三' 结果数量: {len(search_results) if search_results else 0}")
            
            if search_results:
                for i, result in enumerate(search_results[:3]):
                    print(f"     {i+1}. {result.get('source')} --[{result.get('relationship')}]--> {result.get('destination')}")
            
            # 测试获取所有数据
            all_data = memory.graph.get_all(filters=filters, limit=20)
            print(f"   获取所有数据结果数量: {len(all_data) if all_data else 0}")
            
        except Exception as e:
            print(f"   ❌ 测试图搜索失败: {e}")
        
        # 8. 检查动态实体管理器的保存
        print("\n8. 🔧 检查动态实体管理器:")
        try:
            from dynamic_entity_manager import DynamicEntityManager
            entity_manager = DynamicEntityManager(memory)
            
            # 测试处理一个简单的情节
            test_text = "测试用户是一名软件工程师，专门研究人工智能技术。"
            episode = await entity_manager.process_episode(test_text, "debug_user")
            
            print(f"   测试情节处理:")
            print(f"     提取实体数量: {len(episode.entities)}")
            print(f"     提取关系数量: {len(episode.relations)}")
            
            # 检查是否保存到图数据库
            debug_filters = {'user_id': 'debug_user'}
            debug_search = memory.graph.search("测试用户", filters=debug_filters, limit=5)
            print(f"     图数据库中的搜索结果: {len(debug_search) if debug_search else 0}")
            
        except Exception as e:
            print(f"   ❌ 动态实体管理器测试失败: {e}")
        
        print("\n" + "=" * 60)
        print("诊断完成！")
        
    except Exception as e:
        print(f"❌ 诊断过程失败: {e}")
        import traceback
        traceback.print_exc()


async def test_mixed_search_sorting():
    """测试混合搜索的排序功能"""
    print("\n🔄 === 测试混合搜索排序 ===")
    
    try:
        memory = await get_memory_client()
        if not memory:
            print("❌ 内存客户端初始化失败")
            return
        
        from dynamic_entity_manager import DynamicEntityManager
        entity_manager = DynamicEntityManager(memory)
        
        # 执行混合搜索
        query = "张三"
        user_id = "test_user"
        
        result = await entity_manager.hybrid_search(query, user_id, limit=10)
        
        print(f"混合搜索结果:")
        print(f"  状态: {result.get('status')}")
        print(f"  查询: {query}")
        
        if result.get('status') == 'success':
            results = result.get('results', [])
            print(f"  结果数量: {len(results)}")
            
            print(f"  排序后的结果:")
            for i, item in enumerate(results[:5]):  # 显示前5个
                score = item.get('score', 0)
                memory_text = item.get('memory', '')[:100]
                print(f"    {i+1}. 相关度: {score:.3f} - {memory_text}...")
        
    except Exception as e:
        print(f"❌ 混合搜索测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(debug_graph_database())
    asyncio.run(test_mixed_search_sorting())
