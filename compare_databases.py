#!/usr/bin/env python3
"""
对比向量数据库和图数据库的存储内容
"""

import asyncio
from app import get_memory_client


async def compare_databases():
    """对比两个数据库的存储内容"""
    memory = await get_memory_client()
    
    print("🔍 === 向量数据库 vs 图数据库对比 ===")
    print("=" * 60)
    
    # 1. 向量数据库内容
    print("\n📊 向量数据库 (Qdrant) 存储内容:")
    print("-" * 40)
    
    vector_results = await memory.search("张三", user_id="default_user", limit=3)
    if isinstance(vector_results, dict) and 'results' in vector_results:
        for i, result in enumerate(vector_results['results']):
            print(f"{i+1}. ID: {result.get('id')}")
            print(f"   内容: {result.get('memory')}")
            print(f"   用户: {result.get('user_id')}")
            print(f"   相似度: {result.get('score', 0):.3f}")
            print(f"   创建时间: {result.get('created_at')}")
            print()
    
    # 2. 图数据库内容
    print("\n🕸️ 图数据库 (Neo4j) 存储内容:")
    print("-" * 40)
    
    # 查询所有实体
    print("实体 (节点):")
    entity_query = '''
    MATCH (n:__Entity__ {user_id: "default_user"})
    RETURN n.name as name, n.type as type, n.mentions as mentions
    ORDER BY n.mentions DESC
    LIMIT 10
    '''
    entities = memory.graph.graph.query(entity_query)
    for i, entity in enumerate(entities):
        print(f"  {i+1}. {entity.get('name')} [{entity.get('type')}] (提及: {entity.get('mentions', 1)})")
    
    print(f"\n关系 (边):")
    relation_query = '''
    MATCH (a:__Entity__ {user_id: "default_user"})-[r]->(b:__Entity__ {user_id: "default_user"})
    RETURN a.name as source, type(r) as relationship, b.name as destination
    LIMIT 10
    '''
    relations = memory.graph.graph.query(relation_query)
    for i, rel in enumerate(relations):
        print(f"  {i+1}. {rel.get('source')} --[{rel.get('relationship')}]--> {rel.get('destination')}")
    
    # 3. 功能对比
    print(f"\n⚖️ 功能对比:")
    print("-" * 40)
    
    print("向量数据库 (Qdrant):")
    print("  ✅ 语义相似度搜索")
    print("  ✅ 模糊匹配和同义词理解")
    print("  ✅ 完整内容检索")
    print("  ✅ 快速全文搜索")
    print("  ❌ 无法理解实体关系")
    print("  ❌ 无法进行关系推理")
    
    print("\n图数据库 (Neo4j):")
    print("  ✅ 实体关系建模")
    print("  ✅ 复杂关系查询")
    print("  ✅ 路径分析和推理")
    print("  ✅ 结构化知识表示")
    print("  ❌ 无语义理解能力")
    print("  ❌ 无法模糊匹配")
    
    # 4. 协同效果演示
    print(f"\n🤝 协同搜索演示:")
    print("-" * 40)
    
    from dynamic_entity_manager import DynamicEntityManager
    entity_manager = DynamicEntityManager(memory)
    
    # 测试不同类型的查询
    test_queries = [
        "机器学习专家",  # 语义查询
        "张三的技能",    # 关系查询
        "Python开发"     # 混合查询
    ]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        result = await entity_manager.hybrid_search(query, "default_user", limit=3)
        
        vector_count = result.get('vector_count', 0)
        graph_count = result.get('graph_count', 0)
        
        print(f"  向量结果: {vector_count} 条")
        print(f"  图结果: {graph_count} 条")
        print(f"  总结果: {len(result.get('results', []))} 条")
        
        # 显示结果类型分布
        sources = {}
        for item in result.get('results', []):
            source = item.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        
        print(f"  结果分布: {sources}")


if __name__ == "__main__":
    asyncio.run(compare_databases())
