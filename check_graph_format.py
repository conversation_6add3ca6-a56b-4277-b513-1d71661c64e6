#!/usr/bin/env python3
"""
检查图搜索结果格式
"""

import asyncio
from app import get_memory_client


async def check_graph_format():
    memory = await get_memory_client()
    filters = {'user_id': 'default_user'}
    graph_results = memory.graph.search('张三', filters=filters, limit=3)
    
    print('图搜索结果格式:')
    print(f'类型: {type(graph_results)}')
    print(f'数量: {len(graph_results) if graph_results else 0}')
    
    if graph_results:
        for i, result in enumerate(graph_results):
            print(f'{i+1}. {result}')
            if isinstance(result, dict):
                print(f'   键: {list(result.keys())}')
                print(f'   ID: {result.get("id", "None")}')
                print(f'   memory: {result.get("memory", "None")}')
                print(f'   source: {result.get("source", "None")}')
                print(f'   destination: {result.get("destination", "None")}')
            else:
                print(f'   类型: {type(result)}')


if __name__ == "__main__":
    asyncio.run(check_graph_format())
