#!/usr/bin/env python3
"""
调试 None 结果问题
"""

import asyncio
from app import get_memory_client
from dynamic_entity_manager import DynamicEntityManager


async def debug_search_results():
    """调试混合搜索的 None 结果问题"""
    memory = await get_memory_client()
    entity_manager = DynamicEntityManager(memory)
    
    print("🔍 调试混合搜索的 None 结果问题")
    print("=" * 50)
    
    # 1. 测试向量搜索
    print("\n1. 测试向量搜索:")
    vector_results = await memory.search("张三", user_id="test_user", limit=5)
    print(f"   向量搜索结果类型: {type(vector_results)}")
    print(f"   向量搜索结果: {vector_results}")
    
    if isinstance(vector_results, dict) and 'results' in vector_results:
        print(f"   向量结果数量: {len(vector_results['results'])}")
        for i, result in enumerate(vector_results['results'][:3]):
            print(f"     {i+1}. ID: {result.get('id')}")
            print(f"        内容: {result.get('memory', 'None')[:50]}...")
            print(f"        分数: {result.get('score', 0)}")
    
    # 2. 测试图搜索
    print("\n2. 测试图搜索:")
    if hasattr(memory, 'graph') and memory.graph:
        filters = {'user_id': 'test_user'}
        graph_results = memory.graph.search("张三", filters=filters, limit=5)
        print(f"   图搜索结果类型: {type(graph_results)}")
        print(f"   图搜索结果数量: {len(graph_results) if graph_results else 0}")
        
        if graph_results:
            for i, result in enumerate(graph_results[:3]):
                print(f"     {i+1}. 来源: {result.get('source')}")
                print(f"        目标: {result.get('destination')}")
                print(f"        关系: {result.get('relationship')}")
                print(f"        ID: {result.get('id', 'None')}")
                print(f"        内容: {result.get('memory', 'None')}")
    
    # 3. 测试混合搜索
    print("\n3. 测试混合搜索:")
    hybrid_result = await entity_manager.hybrid_search('张三', 'test_user', limit=10)
    
    print(f"   状态: {hybrid_result.get('status')}")
    print(f"   向量结果数: {hybrid_result.get('vector_count')}")
    print(f"   图结果数: {hybrid_result.get('graph_count')}")
    print(f"   总结果数: {hybrid_result.get('total_count')}")
    
    print("\n   详细结果:")
    for i, item in enumerate(hybrid_result.get('results', [])[:8]):
        source = item.get('source', 'Unknown')
        item_id = item.get('id', 'None')
        memory_content = item.get('memory', 'None')
        score = item.get('final_score', item.get('score', 0))
        
        print(f"     {i+1}. 来源: {source}")
        print(f"        ID: {item_id}")
        if memory_content and memory_content != 'None':
            print(f"        内容: {str(memory_content)[:50]}...")
        else:
            print(f"        内容: None")
        print(f"        分数: {score:.3f}")
        
        # 如果是 None 结果，显示原始数据
        if item_id == 'None' or memory_content == 'None':
            print(f"        ⚠️ 原始数据: {item}")
        print()


if __name__ == "__main__":
    asyncio.run(debug_search_results())
