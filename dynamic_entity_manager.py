#!/usr/bin/env python3
"""
动态实体关系管理系统 - 基于Graphiti的动态类型处理
支持任意实体类型和关系类型，无需硬编码枚举
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# 预定义的常见实体类型（可扩展）
COMMON_ENTITY_TYPES = {
    # 人员与组织
    "PERSON", "TEAM", "ROLE", "ORGANIZATION", "COMPANY",
    # 项目管理
    "PROJECT", "FEATURE", "TASK", "BUG", "MILESTONE", "RELEASE", "REQUIREMENT",
    # 代码与版本控制
    "REPOSITORY", "BRANCH", "COMMIT", "PULL_REQUEST", "CODE_FILE", "FUNCTION", "CLASS",
    # 技术栈
    "PROGRAMMING_LANGUAGE", "FRAMEWORK", "LIBRARY", "DATABASE", "PLATFORM", "TOOL",
    # 基础设施
    "ENVIRONMENT", "SERVER", "CLOUD_SERVICE", "CONTAINER", "DEPLOYMENT",
    # 测试与质量
    "TEST_CASE", "TEST_SUITE", "BUG_REPORT", "SECURITY_VULNERABILITY",
    # 文档
    "DOCUMENT", "API_SPECIFICATION", "MEETING_NOTES"
}

# 预定义的常见关系类型（可扩展）
COMMON_RELATION_TYPES = {
    # 人员与组织关系
    "MEMBER_OF", "WORKS_FOR", "MANAGES", "COLLABORATES_WITH", "REPORTS_TO",
    # 项目管理关系
    "PART_OF", "BELONGS_TO", "ASSIGNED_TO", "BLOCKS", "DEPENDS_ON",
    # 代码与开发关系
    "AUTHORED_BY", "REVIEWED_BY", "IMPLEMENTS", "FIXES", "MODIFIES", "CONTAINS",
    # 技术关系
    "USES", "INTEGRATES_WITH", "EXTENDS", "CALLS", "INHERITS_FROM",
    # 基础设施关系
    "DEPLOYED_TO", "HOSTED_ON", "RUNS_IN", "TRIGGERED_BY",
    # 测试关系
    "TESTS", "FOUND_BY", "DETECTED",
    # 文档关系
    "DOCUMENTS", "REFERENCES", "CREATED_BY",
    # 通用关系
    "IS_A", "RELATES_TO", "SKILLED_IN", "EXPERT_IN", "IDENTIFIES_AS", "RESPONSIBLE_FOR"
}

@dataclass
class DynamicEntity:
    """动态实体数据类"""
    name: str
    entity_type: str  # 动态字符串类型
    properties: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    confidence: float = 1.0
    
@dataclass
class DynamicRelation:
    """动态关系数据类"""
    source_entity: str
    target_entity: str
    relation_type: str  # 动态字符串类型
    properties: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    valid_from: datetime
    confidence: float = 1.0
    valid_to: Optional[datetime] = None

@dataclass
class DynamicEpisode:
    """动态情节数据类"""
    content: str
    episode_id: str
    user_id: str
    created_at: datetime
    entities: List[DynamicEntity]
    relations: List[DynamicRelation]
    metadata: Dict[str, Any]

class DynamicEntityManager:
    """动态实体关系管理器 - 支持任意类型"""
    
    def __init__(self, memory_client):
        self.memory = memory_client
        self.logger = logging.getLogger(__name__)
        
    async def process_episode(self, content: str, user_id: str, metadata: Optional[Dict] = None) -> DynamicEpisode:
        """
        处理情节 - 动态提取实体关系
        """
        episode_id = f"episode_{datetime.now().isoformat()}_{user_id}"
        created_at = datetime.now()
        
        try:
            # 动态提取实体和关系
            entities, relations = await self._extract_entities_and_relations_global(content, user_id)
            
            # 创建情节对象
            episode = DynamicEpisode(
                content=content,
                episode_id=episode_id,
                user_id=user_id,
                created_at=created_at,
                entities=entities,
                relations=relations,
                metadata=metadata or {}
            )

            # 🔧 修复：将提取的实体和关系保存到图数据库
            await self._save_entities_to_graph(entities, relations, user_id)

            self.logger.info(f"动态情节处理完成: {episode_id}, 实体: {len(entities)}, 关系: {len(relations)}")
            return episode
            
        except Exception as e:
            self.logger.error(f"动态情节处理失败: {e}")
            # 返回空的情节对象而不是抛出异常
            return DynamicEpisode(
                content=content,
                episode_id=episode_id,
                user_id=user_id,
                created_at=created_at,
                entities=[],
                relations=[],
                metadata=metadata or {}
            )

    async def process_episode_global(self, content: str, user_id: str, metadata: Dict = None) -> DynamicEpisode:
        """
        🔧 长记忆全局处理 - 保持实体间的完整关系
        适用于长文本，先进行全局实体提取，再保存到图数据库
        """
        episode_id = f"global_episode_{datetime.now().isoformat()}_{user_id}"
        created_at = datetime.now()

        try:
            # 🔥 全局实体提取 - 一次性提取所有实体和关系
            entities, relations = await self._extract_entities_and_relations_global(content, user_id)

            # 创建全局情节对象
            episode = DynamicEpisode(
                content=content,
                episode_id=episode_id,
                user_id=user_id,
                created_at=created_at,
                entities=entities,
                relations=relations,
                metadata=metadata or {}
            )

            # 🔧 保存全局实体关系到图数据库
            await self._save_entities_to_graph(entities, relations, user_id)

            self.logger.info(f"全局情节处理完成: {episode_id}, 实体: {len(entities)}, 关系: {len(relations)}")
            return episode

        except Exception as e:
            self.logger.error(f"全局情节处理失败: {e}")
            # 返回空的情节对象
            return DynamicEpisode(
                content=content,
                episode_id=episode_id,
                user_id=user_id,
                created_at=created_at,
                entities=[],
                relations=[],
                metadata=metadata or {}
            )

    async def _extract_entities_and_relations_global(self, content: str, user_id: str) -> Tuple[List[DynamicEntity], List[DynamicRelation]]:
        """
        🔥 全局实体关系提取 - 专门处理长文本
        使用更强的提示词和解析逻辑，保持实体间的完整关系
        """
        entities = []
        relations = []

        try:
            # 🔥 直接使用LLM进行JSON格式提取，简化流程
            if hasattr(self.memory, 'llm') and self.memory.llm:
                self.logger.info(f"使用LLM进行结构化提取，文本长度: {len(content)}")

                # 构建简单的JSON提示词
                prompt = self._build_simple_json_prompt(content)

                # 获取LLM响应
                llm_response = self.memory.llm.generate_response(
                    messages=[{"role": "user", "content": prompt}]
                )

                # 解析JSON响应
                result = self._parse_simple_json(llm_response)

                if result and (result.get('entities') or result.get('relations')):
                    # 转换为 DynamicEntity 和 DynamicRelation
                    current_time = datetime.now()
                    entities = []
                    relations = []

                    # 解析实体
                    for entity_data in result.get('entities', []):
                        entities.append(DynamicEntity(
                            name=entity_data.get('name', ''),
                            entity_type=entity_data.get('type', 'UNKNOWN'),
                            properties={},
                            created_at=current_time,
                            updated_at=current_time,
                            confidence=entity_data.get('confidence', 0.8)
                        ))

                    # 解析关系
                    for relation_data in result.get('relations', []):
                        relations.append(DynamicRelation(
                            source_entity=relation_data.get('source', ''),
                            target_entity=relation_data.get('target', ''),
                            relation_type=relation_data.get('type', 'RELATES_TO'),
                            properties={},
                            created_at=current_time,
                            updated_at=current_time,
                            valid_from=current_time,
                            confidence=relation_data.get('confidence', 0.8)
                        ))

                    self.logger.info(f"JSON提取成功: {len(entities)} 个实体, {len(relations)} 个关系")
                    return entities, relations
                else:
                    self.logger.warning("JSON解析返回空结果")
            else:
                self.logger.error("LLM未初始化")

        except Exception as e:
            self.logger.error(f"JSON实体提取失败: {e}")
            import traceback
            self.logger.debug(f"错误详情: {traceback.format_exc()}")

        return entities, relations



    async def _save_entities_to_graph(self, entities: List[DynamicEntity], relations: List[DynamicRelation], user_id: str):
        """
        将提取的实体和关系保存到图数据库
        """
        try:
            # 检查是否有图数据库
            if not hasattr(self.memory, 'graph') or not self.memory.graph:
                self.logger.warning("图数据库未启用，跳过实体关系保存")
                return

            # 🔧 修复：正确构建图数据库所需的数据格式
            entities_to_add = []
            entity_type_map = {}

            # 构建实体类型映射
            for entity in entities:
                entity_type_map[entity.name] = entity.entity_type

            # 🔧 修复：使用统一的 __Entity__ 标签，同时保存动态类型信息
            # 先添加实体节点（使用 __Entity__ 标签）
            for entity in entities:
                entities_to_add.append({
                    'source': entity.name,
                    'destination': entity.entity_type,
                    'relationship': 'IS_A'
                })

            # 再添加关系
            for relation in relations:
                entities_to_add.append({
                    'source': relation.source_entity,
                    'destination': relation.target_entity,
                    'relationship': relation.relation_type
                })

            # 批量添加到图数据库
            if entities_to_add:
                try:
                    filters = {'user_id': user_id}
                    # 🔧 使用正确的参数格式调用 _add_entities
                    result = self.memory.graph._add_entities(entities_to_add, filters, entity_type_map)
                    self.logger.info(f"成功保存 {len(entities_to_add)} 个关系到图数据库")
                    self.logger.debug(f"保存结果: {result}")
                except Exception as e:
                    self.logger.warning(f"图数据库保存失败: {e}")
                    # 如果保存失败，记录详细错误信息
                    import traceback
                    self.logger.debug(f"详细错误: {traceback.format_exc()}")

        except Exception as e:
            self.logger.error(f"保存实体关系到图数据库失败: {e}")

    def _build_simple_json_prompt(self, content: str) -> str:
        """构建优化的JSON提示词 - 提高实体关系提取准确性"""
        return f"""# 高精度实体关系提取指令

## 核心任务
从文本中准确提取实体和关系，构建高质量知识图谱。

## 实体提取规则
1. **准确性优先**：只提取文本中明确提到的实体，不推测或添加未提及内容
2. **名称一致性**：实体名称必须与文本表述完全一致
3. **类型标准化**：严格按照预定义类型分类
4. **置信度评估**：基于文本明确程度设置（0.7-1.0）

## 标准实体类型
- **PERSON**：具体人名（如：张三、李四）
- **ORGANIZATION**：公司、学校、机构（如：阿里巴巴、清华大学）
- **LOCATION**：地点、城市（如：北京、上海）
- **TECHNOLOGY**：技术、工具、框架（如：Python、Django、TensorFlow）
- **SKILL**：技能、能力（如：机器学习、数据分析）
- **PROJECT**：项目名称（如：电商平台、推荐系统）
- **CONCEPT**：概念、领域（如：人工智能、深度学习）
- **EDUCATION**：教育背景（如：计算机科学与技术专业）

## 关系提取规则
1. **明确依据**：关系必须有明确的文本依据，不推测隐含关系
2. **方向正确**：确保关系方向符合语义逻辑
3. **类型准确**：严格按照预定义关系类型分类
4. **高置信度**：优先提取置信度>0.8的关系

## 标准关系类型
- **WORKS_AT**：工作关系（人物→组织）
- **LOCATED_IN**：地理位置（实体→地点）
- **USES**：使用关系（人物→技术/工具）
- **SPECIALIZES_IN**：专长关系（人物→技能/领域）
- **GRADUATED_FROM**：教育关系（人物→学校）
- **RESPONSIBLE_FOR**：职责关系（人物→项目/职责）
- **COLLABORATES_WITH**：合作关系（人物→人物）
- **IS_EXPERT_IN**：专家关系（人物→技术/领域）
- **STUDIES**：学习关系（人物→专业/技能）
- **KNOWS**：了解关系（人物→技术/概念）

## 质量控制标准
- 最低置信度：0.7
- 优先高置信度（>0.8）实体和关系
- 避免重复或冗余实体
- 确保实体名称的一致性引用

## 输入文本
{content}

## 输出格式
严格按照以下JSON格式返回，不要添加任何解释或格式化：

{{
  "entities": [
    {{
      "name": "实体名称",
      "type": "实体类型",
      "confidence": 0.9
    }}
  ],
  "relations": [
    {{
      "source": "源实体名称",
      "target": "目标实体名称",
      "type": "关系类型",
      "confidence": 0.8
    }}
  ]
}}

**重要**：只返回有效JSON，不要任何解释或markdown格式。"""

    def _parse_simple_json(self, response: str) -> Optional[dict]:
        """简单的JSON解析方法"""
        try:
            import json
            import re

            # 清理响应文本
            response_text = str(response).strip()
            self.logger.debug(f"🔍 LLM响应长度: {len(response_text)} 字符")

            # 尝试直接解析JSON
            try:
                data = json.loads(response_text)
                self.logger.info("✅ 直接JSON解析成功")
                return data
            except json.JSONDecodeError:
                pass

            # 尝试提取JSON代码块
            json_blocks = re.findall(r'```json\s*(.*?)\s*```', response_text, re.DOTALL | re.IGNORECASE)
            for json_block in json_blocks:
                try:
                    data = json.loads(json_block.strip())
                    self.logger.info("✅ JSON代码块解析成功")
                    return data
                except json.JSONDecodeError:
                    continue

            # 尝试提取JSON对象
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group()
                try:
                    data = json.loads(json_text)
                    self.logger.info("✅ JSON对象解析成功")
                    return data
                except json.JSONDecodeError as e:
                    self.logger.warning(f"JSON解析失败: {e}")

            self.logger.warning("无法解析JSON响应")
            return None

        except Exception as e:
            self.logger.error(f"JSON解析失败: {e}")
            return None
    
    async def hybrid_search(self, query: str, user_id: str, limit: int = 10) -> Dict[str, Any]:
        """
        动态混合搜索 - 结合向量搜索和图遍历
        """
        try:
            # 1. 向量语义搜索
            vector_results = await self.memory.search(query, user_id=user_id, limit=limit*2)
            
            # 2. 图数据库搜索
            graph_results = []
            if hasattr(self.memory, 'graph') and self.memory.graph:
                filters = {'user_id': user_id}
                graph_results = self.memory.graph.search(query, filters=filters, limit=limit)
            
            # 3. 结合和重排序结果
            combined_results, stats = self._combine_search_results(vector_results, graph_results, query)

            return {
                'status': 'success',
                'results': combined_results[:limit],
                'vector_count': stats['vector_count'],
                'graph_count': stats['graph_count'],
                'total_count': stats['total_count'],
                'duplicates_removed': stats['duplicates_removed']
            }
            
        except Exception as e:
            self.logger.error(f"动态混合搜索失败: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'results': []
            }
    
    def _combine_search_results(self, vector_results: Dict, graph_results: List, query: str) -> tuple:
        """🔥 改进：智能结合向量和图搜索结果，按重要性和相关度排序"""
        combined = []
        seen_memories = set()

        # 统计信息
        original_vector_count = len(vector_results.get('results', []))
        original_graph_count = len(graph_results)
        duplicates_removed = 0

        # 添加向量搜索结果
        vector_added = 0
        if isinstance(vector_results, dict) and 'results' in vector_results:
            for result in vector_results['results']:
                memory_id = result.get('id')
                if memory_id not in seen_memories:
                    seen_memories.add(memory_id)
                    vector_added += 1

                    # 计算重要性分数
                    importance = self._calculate_importance(result)
                    relevance = result.get('score', 0)

                    # 综合分数 = 相关度 * 0.7 + 重要性 * 0.3
                    final_score = relevance * 0.7 + importance * 0.3

                    result.update({
                        'source': 'vector',
                        'importance': importance,
                        'relevance': relevance,
                        'final_score': final_score
                    })
                    combined.append(result)
                else:
                    duplicates_removed += 1

        # 🔧 修复：正确处理图搜索结果格式
        graph_added = 0
        for result in graph_results:
            # 图搜索结果格式: {'source': '张三', 'relationship': 'USES', 'destination': 'TensorFlow'}
            source_entity = result.get('source', '')
            destination_entity = result.get('destination', '')
            relationship = result.get('relationship', '')

            # 创建唯一标识符
            graph_key = f"{source_entity}-{relationship}-{destination_entity}"
            if graph_key not in seen_memories:
                seen_memories.add(graph_key)
                graph_added += 1

                # 图关系的重要性基于关系类型和实体
                graph_importance = self._calculate_graph_importance(result)
                graph_relevance = self._calculate_graph_relevance(result, query)

                # 图结果的综合分数
                final_score = graph_relevance * 0.6 + graph_importance * 0.4

                # 🔧 修复：为图搜索结果创建正确的格式
                graph_item = {
                    'id': f"graph_{hash(graph_key) % 1000000}",  # 生成唯一ID
                    'memory': f"{source_entity} {relationship} {destination_entity}",  # 生成可读内容
                    'source': 'graph',
                    'graph_source': source_entity,
                    'graph_destination': destination_entity,
                    'graph_relationship': relationship,
                    'importance': graph_importance,
                    'relevance': graph_relevance,
                    'final_score': final_score,
                    'score': final_score  # 兼容性
                }
                combined.append(graph_item)
            else:
                duplicates_removed += 1

        # 🔥 混合得分归一化系统
        normalized_results = self._normalize_and_mix_scores(combined, query)

        # 🔥 添加质量过滤机制，过滤低价值信息
        QUALITY_THRESHOLD = 0.3  # 质量门槛：归一化得分低于0.3的结果将被过滤
        MAX_RESULTS = 100        # 增加最大结果数，支持长记忆的完整检索

        # 🔧 质量过滤：基于得分过滤低价值内容
        quality_filtered_results = []
        for result in normalized_results:
            memory_text = result.get('memory', '').strip()
            normalized_score = result.get('normalized_score', 0)

            # 过滤条件：
            # 1. 归一化得分高于质量门槛
            # 2. 内容长度合理（不是过短的片段）
            # 3. 内容不为空
            if (normalized_score >= QUALITY_THRESHOLD and
                len(memory_text) > 5 and
                memory_text.lower() not in ['n/a', 'null', 'none', '']):
                quality_filtered_results.append(result)

        # 限制结果数量
        final_results = quality_filtered_results[:MAX_RESULTS]

        # 准备统计信息
        quality_filtered_count = len(normalized_results) - len(quality_filtered_results)
        stats = {
            'vector_count': vector_added,
            'graph_count': graph_added,
            'total_count': len(final_results),
            'duplicates_removed': duplicates_removed,
            'quality_filtered': quality_filtered_count,
            'original_vector_count': original_vector_count,
            'original_graph_count': original_graph_count
        }

        self.logger.info(f"混合搜索统计: 向量 {vector_added}/{original_vector_count}, 图 {graph_added}/{original_graph_count}, 去重 {duplicates_removed}, 质量过滤 {quality_filtered_count}, 总计 {len(final_results)}")

        return final_results, stats

    def _calculate_importance(self, result: Dict) -> float:
        """计算记忆的重要性分数 - 通用版本"""
        try:
            memory_text = result.get('memory', '')

            # 🔄 恢复简单通用的计算方式
            # 1. 基于文本长度的重要性（线性增长，更通用）
            length_score = min(len(memory_text) / 200, 1.0)  # 200字符达到满分

            # 2. 基于向量相似度的重要性（如果有的话）
            vector_score = result.get('score', 0.5)  # 使用原始向量相似度

            # 🔥 简化的综合重要性分数
            # 主要依赖向量相似度，文本长度作为辅助
            importance = vector_score * 0.8 + length_score * 0.2

            return min(importance, 1.0)

        except Exception:
            return 0.5  # 默认中等重要性

    def _normalize_and_mix_scores(self, results: List[Dict], query: str) -> List[Dict]:
        """
        🔥 混合得分归一化系统
        整合多种得分，进行归一化处理，并计算最终得分
        """
        if not results:
            return results

        try:
            # 1. 收集所有得分维度
            all_scores = {
                'relevance': [],
                'importance': [],
                'final_score': [],
                'query_match': [],  # 新增：查询匹配度
                'freshness': []     # 新增：新鲜度（基于ID或时间戳）
            }

            # 2. 计算额外得分维度
            for result in results:
                # 查询匹配度：检查查询词在内容中的出现
                query_match_score = self._calculate_query_match_score(result, query)
                result['query_match'] = query_match_score

                # 新鲜度：基于结果的来源和ID（简单启发式）
                freshness_score = self._calculate_freshness_score(result)
                result['freshness'] = freshness_score

                # 收集所有分数
                all_scores['relevance'].append(result.get('relevance', 0))
                all_scores['importance'].append(result.get('importance', 0))
                all_scores['final_score'].append(result.get('final_score', 0))
                all_scores['query_match'].append(query_match_score)
                all_scores['freshness'].append(freshness_score)

            # 3. 归一化各个得分维度
            normalized_scores = {}
            for score_type, scores in all_scores.items():
                if scores:
                    min_score = min(scores)
                    max_score = max(scores)
                    if max_score > min_score:
                        # Min-Max归一化到0-1
                        normalized_scores[score_type] = [
                            (score - min_score) / (max_score - min_score)
                            for score in scores
                        ]
                    else:
                        # 所有分数相同，归一化为0.5
                        normalized_scores[score_type] = [0.5] * len(scores)
                else:
                    normalized_scores[score_type] = []

            # 4. 计算混合归一化得分
            for i, result in enumerate(results):
                # 🔥 混合权重配置
                weights = {
                    'relevance': 0.35,      # 相关度 35%
                    'importance': 0.25,     # 重要性 25%
                    'query_match': 0.25,    # 查询匹配 25%
                    'freshness': 0.15       # 新鲜度 15%
                }

                # 计算加权平均
                normalized_score = sum(
                    normalized_scores[score_type][i] * weight
                    for score_type, weight in weights.items()
                    if i < len(normalized_scores[score_type])
                )

                # 更新结果
                result['normalized_score'] = round(normalized_score, 3)
                result['score_breakdown'] = {
                    'relevance_norm': round(normalized_scores['relevance'][i], 3),
                    'importance_norm': round(normalized_scores['importance'][i], 3),
                    'query_match_norm': round(normalized_scores['query_match'][i], 3),
                    'freshness_norm': round(normalized_scores['freshness'][i], 3)
                }

            # 5. 按归一化得分排序
            results.sort(key=lambda x: x.get('normalized_score', 0), reverse=True)

            return results

        except Exception as e:
            self.logger.warning(f"得分归一化失败: {e}")
            # 失败时使用原始final_score排序
            results.sort(key=lambda x: x.get('final_score', 0), reverse=True)
            for result in results:
                result['normalized_score'] = result.get('final_score', 0)
            return results

    def _calculate_query_match_score(self, result: Dict, query: str) -> float:
        """计算查询匹配度"""
        try:
            memory_text = result.get('memory', '').lower()
            query_lower = query.lower()

            if not memory_text or not query_lower:
                return 0.0

            # 1. 完整查询匹配
            if query_lower in memory_text:
                return 1.0

            # 2. 查询词分词匹配
            query_words = query_lower.split()
            if not query_words:
                return 0.0

            matched_words = sum(1 for word in query_words if word in memory_text)
            word_match_ratio = matched_words / len(query_words)

            # 3. 字符重叠度
            common_chars = set(query_lower) & set(memory_text)
            char_overlap = len(common_chars) / max(len(set(query_lower)), 1)

            # 综合匹配分数
            match_score = word_match_ratio * 0.7 + char_overlap * 0.3

            return min(match_score, 1.0)

        except Exception:
            return 0.0

    def _calculate_freshness_score(self, result: Dict) -> float:
        """计算新鲜度分数"""
        try:
            # 简单启发式：基于来源和ID
            source = result.get('source', '')
            result_id = result.get('id', '')

            # 向量搜索结果通常更新鲜（有完整ID）
            if source == 'vector' and result_id and len(result_id) > 10:
                return 0.8

            # 图搜索结果相对较旧
            if source == 'graph':
                return 0.6

            # 默认中等新鲜度
            return 0.7

        except Exception:
            return 0.5

    def _calculate_graph_importance(self, result: Dict) -> float:
        """计算图关系的重要性分数 - 使用统一的重要性计算"""
        try:
            # 导入app.py中的统一函数
            import sys
            import os
            sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
            from app import _calculate_entity_importance_score

            relationship = result.get('relationship', 'RELATES_TO')
            entity = result.get('source', '') or result.get('destination', '')

            return _calculate_entity_importance_score(entity, relationship)

        except Exception:
            return 0.5

    def _calculate_graph_relevance(self, result: Dict, query: str) -> float:
        """计算图关系与查询的相关性"""
        try:
            source = result.get('source', '').lower()
            destination = result.get('destination', '').lower()
            query_lower = query.lower()

            # 检查查询词是否出现在实体名称中
            relevance = 0.0
            if query_lower in source:
                relevance += 0.5
            if query_lower in destination:
                relevance += 0.5

            # 部分匹配
            query_words = query_lower.split()
            for word in query_words:
                if len(word) > 2:  # 忽略太短的词
                    if word in source:
                        relevance += 0.2
                    if word in destination:
                        relevance += 0.2

            return min(relevance, 1.0)

        except Exception:
            return 0.3  # 默认低相关性
