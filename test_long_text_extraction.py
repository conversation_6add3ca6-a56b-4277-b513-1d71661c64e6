#!/usr/bin/env python3
"""
测试长文本实体提取功能
验证修复后的全局实体提取是否正常工作
"""

import asyncio
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from app import add_memory, get_memory_client
from dynamic_entity_manager import DynamicEntityManager


async def test_long_text_extraction():
    """测试长文本实体提取"""
    print("🔍 === 测试长文本实体提取功能 ===")
    print("=" * 60)
    
    # 准备测试数据
    long_text = """
王小明是一位拥有15年丰富经验的全栈软件架构师，目前担任北京某知名互联网公司的技术总监职位。他在软件开发领域有着深厚的技术功底，精通多种编程语言包括Java、Python、JavaScript、Go和Rust。在前端技术方面，王小明熟练掌握React、Vue.js、Angular等现代前端框架，同时对TypeScript、Webpack、Vite等构建工具有深入理解。后端开发方面，他擅长Spring Boot、Django、Express.js、Gin等框架，并且在微服务架构设计上有丰富实践经验。数据库技术是王小明的另一个强项，他精通MySQL、PostgreSQL、MongoDB、Redis等多种数据库系统，对数据库优化、分库分表、读写分离等高级技术有深入研究。在云计算和DevOps领域，王小明熟练使用AWS、阿里云、腾讯云等云平台服务，掌握Docker、Kubernetes容器化技术，熟悉Jenkins、GitLab CI/CD等持续集成工具。他还具备丰富的系统架构设计经验，曾主导设计过多个日活千万级的大型分布式系统，对高并发、高可用、高性能系统架构有深刻理解。王小明不仅技术能力出众，在团队管理方面也表现优秀，他目前管理着一个30人的技术团队，包括前端工程师、后端工程师、移动端开发工程师、测试工程师和运维工程师。
    """.strip()
    
    short_text = "李四是前端工程师，在上海工作。"
    
    try:
        print(f"\n1. 📝 测试长文本处理 ({len(long_text)} 字符)")
        print(f"   文本预览: {long_text[:100]}...")
        
        # 测试长文本添加
        result_long = await add_memory(long_text, user_id="test_user")
        
        print(f"   ✅ 长文本处理结果:")
        print(f"      状态: {result_long.get('status')}")
        print(f"      实体数量: {result_long.get('entities_count', 0)}")
        print(f"      关系数量: {result_long.get('relations_count', 0)}")
        print(f"      合并结果: {result_long.get('merge_results', {})}")
        
        print(f"\n2. 📝 测试短文本处理 ({len(short_text)} 字符)")
        print(f"   文本: {short_text}")
        
        # 测试短文本添加
        result_short = await add_memory(short_text, user_id="test_user")
        
        print(f"   ✅ 短文本处理结果:")
        print(f"      状态: {result_short.get('status')}")
        print(f"      实体数量: {result_short.get('entities_count', 0)}")
        print(f"      关系数量: {result_short.get('relations_count', 0)}")
        
        print(f"\n3. 🔍 验证图数据库中的实体关系")
        
        # 获取内存客户端
        memory = await get_memory_client()
        
        if hasattr(memory, 'graph') and memory.graph:
            # 查询所有实体
            entity_query = '''
            MATCH (n:__Entity__ {user_id: "test_user"})
            RETURN n.name as name, n.type as type, n.mentions as mentions
            ORDER BY n.mentions DESC
            LIMIT 20
            '''
            entities = memory.graph.graph.query(entity_query)
            
            print(f"   📊 图数据库中的实体 ({len(entities)} 个):")
            for i, entity in enumerate(entities[:10]):
                name = entity.get('name')
                entity_type = entity.get('type')
                mentions = entity.get('mentions', 1)
                print(f"      {i+1}. {name} [{entity_type}] (提及: {mentions})")
            
            # 查询所有关系
            relation_query = '''
            MATCH (a:__Entity__ {user_id: "test_user"})-[r]->(b:__Entity__ {user_id: "test_user"})
            RETURN a.name as source, type(r) as relationship, b.name as destination
            LIMIT 15
            '''
            relations = memory.graph.graph.query(relation_query)
            
            print(f"\n   🔗 图数据库中的关系 ({len(relations)} 个):")
            for i, rel in enumerate(relations[:10]):
                source = rel.get('source')
                relationship = rel.get('relationship')
                destination = rel.get('destination')
                print(f"      {i+1}. {source} --[{relationship}]--> {destination}")
        
        print(f"\n4. 🧪 测试直接全局提取功能")
        
        # 直接测试全局提取
        entity_manager = DynamicEntityManager(memory)
        
        print(f"   测试文本: 张三是资深Python开发工程师，在深圳腾讯公司工作，负责后端API开发。")
        
        episode = await entity_manager.process_episode_global(
            "张三是资深Python开发工程师，在深圳腾讯公司工作，负责后端API开发。",
            "test_user"
        )
        
        print(f"   ✅ 直接全局提取结果:")
        print(f"      实体数量: {len(episode.entities)}")
        print(f"      关系数量: {len(episode.relations)}")
        
        if episode.entities:
            print(f"      提取的实体:")
            for entity in episode.entities:
                print(f"        - {entity.name} [{entity.entity_type}] (置信度: {entity.confidence})")
        
        if episode.relations:
            print(f"      提取的关系:")
            for relation in episode.relations:
                print(f"        - {relation.source_entity} --[{relation.relation_type}]--> {relation.target_entity}")
        
        print(f"\n" + "=" * 60)
        print("✅ 长文本实体提取测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_long_text_extraction())
