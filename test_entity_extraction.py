#!/usr/bin/env python3
"""
测试实体提取功能 - 基于 app.py
用于诊断工具调用问题
"""

import os
import sys
import json
import asyncio

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入 app.py 的功能
from app import (
    add_memory,
    search_memory,
    get_all_memories,
    delete_all_memories,
    extract_entities_from_memory,
    get_entity_relations,
    check_connection_status
)

async def test_connection_status():
    """测试连接状态"""
    print("=== 测试连接状态 ===")

    try:
        status = check_connection_status()
        print(f"连接状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
        return status.get("components", {}).get("memory_client", False)
    except Exception as e:
        print(f"❌ 连接状态检查失败: {e}")
        return False


async def test_add_memory_with_entities():
    """测试添加记忆并提取实体"""
    print("\n=== 测试添加记忆和实体提取 ===")

    # 测试文本
    test_text = "张三是一名资深的机器学习工程师，目前在北京字节跳动公司担任AI算法专家。他专门研究自然语言处理和计算机视觉技术，擅长使用Python、PyTorch和TensorFlow框架。"

    print(f"测试文本: {test_text}")

    try:
        # 添加记忆
        result = await add_memory(test_text, user_id="test_user")

        print(f"添加记忆结果:")
        print(f"  状态: {result.get('status')}")
        print(f"  消息: {result.get('message')}")
        print(f"  实体数量: {result.get('entities_count', 0)}")
        print(f"  关系数量: {result.get('relations_count', 0)}")

        if result.get('entities'):
            print("  提取的实体:")
            for entity in result['entities']:
                print(f"    - {entity.get('name')} [{entity.get('type')}] (置信度: {entity.get('confidence')})")

        if result.get('relations'):
            print("  提取的关系:")
            for relation in result['relations']:
                print(f"    - {relation.get('source')} -> {relation.get('target')} [{relation.get('type')}]")

        return result.get('status') == 'success'

    except Exception as e:
        print(f"❌ 添加记忆失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_entity_relations():
    """测试实体关系查询"""
    print("\n=== 测试实体关系查询 ===")

    try:
        # 查询张三的关系
        result = await get_entity_relations("张三", user_id="test_user")

        print(f"实体关系查询结果:")
        print(f"  状态: {result.get('status')}")
        print(f"  消息: {result.get('message')}")

        relations = result.get('relations', {})
        if relations:
            print(f"  相关记忆数量: {relations.get('memory_count', 0)}")

            graph_relations = relations.get('graph_relations', {})
            print(f"  图数据库实体数量: {graph_relations.get('entity_count', 0)}")
            print(f"  图搜索结果数量: {graph_relations.get('search_count', 0)}")

        return result.get('status') == 'success'

    except Exception as e:
        print(f"❌ 实体关系查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_direct_entity_extraction():
    """测试直接实体提取"""
    print("\n=== 测试直接实体提取 ===")

    test_text = "李四是产品经理，他与张三合作开发智能客服系统。这个项目使用React和Node.js技术。"

    try:
        result = await extract_entities_from_memory(test_text, user_id="test_user")

        print(f"直接实体提取结果:")
        print(f"  状态: {result.get('status')}")
        print(f"  消息: {result.get('message')}")
        print(f"  实体数量: {len(result.get('entities', []))}")

        if result.get('entities'):
            print("  提取的实体:")
            for entity in result['entities']:
                print(f"    - {entity}")

        return result.get('status') == 'success'

    except Exception as e:
        print(f"❌ 直接实体提取失败: {e}")
        import traceback
        traceback.print_exc()
        return False


# 删除了不需要的测试函数


async def main():
    """主测试函数"""
    print("开始实体提取诊断测试 - 基于 app.py")
    print("=" * 60)

    # 检查环境变量
    print("检查环境变量:")
    print(f"OPENAI_API_KEY: {'已设置' if os.getenv('OPENAI_API_KEY') else '未设置'}")
    print(f"OPENAI_API_BASE: {os.getenv('OPENAI_API_BASE', '默认')}")
    print()

    # 1. 测试连接状态
    connection_ok = await test_connection_status()
    if not connection_ok:
        print("❌ 连接失败，停止测试")
        return

    # 2. 清理之前的测试数据
    print("\n清理之前的测试数据...")
    try:
        await delete_all_memories(user_id="test_user")
        print("✅ 清理完成")
    except Exception as e:
        print(f"⚠️ 清理失败: {e}")

    # 3. 测试添加记忆和实体提取
    add_success = await test_add_memory_with_entities()

    # 4. 测试实体关系查询
    if add_success:
        await test_entity_relations()

    # 5. 测试直接实体提取
    await test_direct_entity_extraction()

    # 6. 显示所有记忆
    print("\n=== 查看所有记忆 ===")
    try:
        all_memories = await get_all_memories(user_id="test_user")
        print(f"总记忆数量: {len(all_memories.get('memories', []))}")
    except Exception as e:
        print(f"❌ 获取记忆失败: {e}")

    print("\n" + "=" * 60)
    print("测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
