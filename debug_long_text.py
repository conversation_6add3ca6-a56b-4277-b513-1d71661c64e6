#!/usr/bin/env python3
"""
调试长文本实体提取问题
"""

import asyncio
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from dynamic_entity_manager import DynamicEntityManager
from app import get_memory_client

async def test_long_text():
    memory = await get_memory_client()
    entity_manager = DynamicEntityManager(memory)
    
    long_text = """
王小明是一位拥有15年丰富经验的全栈软件架构师，目前担任北京某知名互联网公司的技术总监职位。他在软件开发领域有着深厚的技术功底，精通多种编程语言包括Java、Python、JavaScript、Go和Rust。在前端技术方面，王小明熟练掌握React、Vue.js、Angular等现代前端框架，同时对TypeScript、Webpack、Vite等构建工具有深入理解。后端开发方面，他擅长Spring Boot、Django、Express.js、Gin等框架，并且在微服务架构设计上有丰富实践经验。数据库技术是王小明的另一个强项，他精通MySQL、PostgreSQL、MongoDB、Redis等多种数据库系统，对数据库优化、分库分表、读写分离等高级技术有深入研究。在云计算和DevOps领域，王小明熟练使用AWS、阿里云、腾讯云等云平台服务，掌握Docker、Kubernetes容器化技术，熟悉Jenkins、GitLab CI/CD等持续集成工具。他还具备丰富的系统架构设计经验，曾主导设计过多个日活千万级的大型分布式系统，对高并发、高可用、高性能系统架构有深刻理解。王小明不仅技术能力出众，在团队管理方面也表现优秀，他目前管理着一个30人的技术团队，包括前端工程师、后端工程师、移动端开发工程师、测试工程师和运维工程师。
    """.strip()
    
    print('🔍 测试长文本Instructor提取...')
    episode = await entity_manager.process_episode_global(long_text, 'test_user')
    print(f'✅ 结果: {len(episode.entities)} 个实体, {len(episode.relations)} 个关系')

if __name__ == "__main__":
    asyncio.run(test_long_text())
