#!/usr/bin/env python3
"""
调试图搜索问题
"""

import asyncio
from app import get_memory_client


async def debug_graph_search():
    memory = await get_memory_client()
    
    # 直接查询图数据库
    print('1. 检查图数据库中的张三相关数据:')
    query1 = 'MATCH (n:__Entity__ {user_id: "test_user"}) WHERE toLower(n.name) CONTAINS "张三" RETURN n.name as name, n.type as type'
    results1 = memory.graph.graph.query(query1)
    print(f'   包含张三的节点: {len(results1)}')
    for r in results1:
        print(f'     - {r.get("name")} ({r.get("type")})')
    
    # 检查所有用户节点
    print('\n2. 检查所有 test_user 的节点:')
    query2 = 'MATCH (n:__Entity__ {user_id: "test_user"}) RETURN n.name as name, n.type as type LIMIT 10'
    results2 = memory.graph.graph.query(query2)
    print(f'   test_user 的节点数: {len(results2)}')
    for r in results2[:5]:
        print(f'     - {r.get("name")} ({r.get("type")})')
    
    # 检查所有关系
    print('\n3. 检查所有 test_user 的关系:')
    query3 = 'MATCH (a:__Entity__ {user_id: "test_user"})-[r]->(b:__Entity__ {user_id: "test_user"}) RETURN a.name as source, type(r) as rel, b.name as dest LIMIT 10'
    results3 = memory.graph.graph.query(query3)
    print(f'   test_user 的关系数: {len(results3)}')
    for r in results3[:5]:
        print(f'     - {r.get("source")} --[{r.get("rel")}]--> {r.get("dest")}')
    
    # 测试图搜索方法
    print('\n4. 测试图搜索方法:')
    filters = {'user_id': 'test_user'}
    search_results = memory.graph.search('张三', filters=filters, limit=10)
    print(f'   图搜索结果: {len(search_results) if search_results else 0}')
    if search_results:
        for r in search_results[:3]:
            print(f'     - {r}')
    else:
        print('     - 无搜索结果')
    
    # 测试 _retrieve_nodes_from_data 方法
    print('\n5. 测试 _retrieve_nodes_from_data 方法:')
    try:
        entity_type_map = memory.graph._retrieve_nodes_from_data('张三', filters)
        print(f'   实体类型映射: {entity_type_map}')
    except Exception as e:
        print(f'   错误: {e}')


if __name__ == "__main__":
    asyncio.run(debug_graph_search())
