{"mcpServers": {"mem0-openmemory": {"command": "python", "args": ["C:/Users/<USER>/mem0/openmemory/api/main.py"], "cwd": "C:/Users/<USER>/mem0/openmemory/api", "env": {"PYTHONPATH": "C:/Users/<USER>/mem0", "OPENAI_API_KEY": "sk-zxcvb1234567890qwertasdfg", "OPENAI_API_URL": "https://gemini-cli-worker-testing.13467879663.workers.dev/v1", "OPENAI_API_MODEL": "gemini-2.5-pro", "EMBEDDINGS_API_KEY": "sk-lvoqzpgtdobtdatsuspkssadssntzovonhciciaqazdxevuf", "EMBEDDINGS_API_URL": "https://api.siliconflow.cn/v1", "EMBEDDINGS_MODEL": "BAAI/bge-large-zh-v1.5", "API_KEY": "sk-zxcvb1234567890qwertasdfg", "USER": "default_user", "POSTGRES_HOST": "localhost", "POSTGRES_PORT": "5432", "POSTGRES_DB": "openmemory", "POSTGRES_USER": "postgres", "POSTGRES_PASSWORD": "1513091437", "POSTGRES_COLLECTION_NAME": "memories", "QDRANT_HOST": "localhost", "QDRANT_PORT": "6333", "QDRANT_COLLECTION": "openmemory_memories", "NEO4J_URI": "neo4j://localhost:7687", "NEO4J_USERNAME": "neo4j", "NEO4J_PASSWORD": "1513091437"}}}}