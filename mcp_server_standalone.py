#!/usr/bin/env python3
"""
独立的OpenMemory MCP服务器
专门为Claude Desktop等MCP客户端设计
"""

import asyncio
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

# 设置环境变量
os.environ.setdefault("PYTHONPATH", "C:/Users/<USER>/mem0")
os.environ.setdefault("OPENAI_API_KEY", "1")
os.environ.setdefault("OPENAI_API_URL", "http://127.0.0.1:8001/v1")
os.environ.setdefault("OPENAI_API_MODEL", "gemini-2.5-flash")
os.environ.setdefault("POSTGRES_HOST", "localhost")
os.environ.setdefault("POSTGRES_PORT", "5432")
os.environ.setdefault("POSTGRES_DB", "openmemory")
os.environ.setdefault("POSTGRES_USER", "postgres")
os.environ.setdefault("POSTGRES_PASSWORD", "1513091437")
os.environ.setdefault("NEO4J_URI", "neo4j://localhost:7687")
os.environ.setdefault("NEO4J_USERNAME", "neo4j")
os.environ.setdefault("NEO4J_PASSWORD", "1513091437")
os.environ.setdefault("USER", "default_user")

# 添加项目路径到Python路径
sys.path.insert(0, "C:/Users/<USER>/mem0")

try:
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import Tool, TextContent
    import mcp.types as types
except ImportError as e:
    print(f"Error importing MCP: {e}", file=sys.stderr)
    print("Please install mcp package: pip install mcp", file=sys.stderr)
    sys.exit(1)

# 导入格式化工具（安全导入）
try:
    from format_utils import format_memory_result
    USE_FORMATTING = True
except ImportError as e:
    logger.warning(f"格式化工具导入失败，使用默认格式: {e}")
    USE_FORMATTING = False

    def format_memory_result(result, operation_type):
        """备用格式化函数"""
        import json
        return json.dumps(result, ensure_ascii=False, indent=2)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建MCP服务器
server = Server("openmemory")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="add_memories",
            description="添加新的记忆到智能知识管理系统（自动进行实体提取和关系识别）。使用场合：1)记录项目信息和相关技术 2)保存学习内容和经验总结 3)记录团队协作和工作进展。系统特点：自动提取实体关系，构建知识图谱，智能合并相似记忆。使用方法：提供完整的描述内容，系统会自动处理并提取实体关系。",
            inputSchema={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "要添加的记忆内容"
                    }
                },
                "required": ["text"]
            }
        ),

        Tool(
            name="list_memories",
            description="列出知识库中的所有记忆条目，显示记忆ID和内容摘要，用于浏览和管理已存储的信息。",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="delete_all_memories",
            description="清空知识库中的所有记忆内容，用于重置或清理整个知识库。注意：此操作不可逆。",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),

        Tool(
            name="get_memory",
            description="获取指定ID的单个记忆的完整详细信息和元数据。用于查看记忆的完整内容、创建时间等详细信息，通常在更新或删除前确认内容。",
            inputSchema={
                "type": "object",
                "properties": {
                    "memory_id": {
                        "type": "string",
                        "description": "要获取的记忆ID，必须是完整的UUID格式，如：a1b2c3d4-e5f6-7890-abcd-ef1234567890"
                    }
                },
                "required": ["memory_id"]
            }
        ),

        Tool(
            name="get_entity_relations",
            description="获取指定实体的关系网络和相关记忆，包含智能推荐功能。分析实体之间的关联关系，发现知识图谱中的连接模式。用于探索实体的关系网络、获取相关信息推荐、分析知识结构。",
            inputSchema={
                "type": "object",
                "properties": {
                    "entity_name": {
                        "type": "string",
                        "description": "要分析的实体名称，如人名、地点、组织名等"
                    }
                },
                "required": ["entity_name"]
            }
        ),


        Tool(
            name="hybrid_search",
            description="智能混合搜索，结合向量语义搜索和图关系搜索，提供多维度得分排序和质量过滤。用于根据关键词查找相关记忆、搜索特定主题内容、发现实体关联。支持模糊匹配和语义理解。",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "搜索查询内容"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回结果数量限制",
                        "default": 10
                    }
                },
                "required": ["query"]
            }
        ),

        Tool(
            name="get_intelligent_recommendations",
            description="获取智能推荐，基于用户兴趣图谱和上下文提供个性化推荐。支持个性化推荐、探索发现推荐、实时推荐等多种模式。用于发现相关内容、推荐学习路径、提供上下文建议。",
            inputSchema={
                "type": "object",
                "properties": {
                    "type": {
                        "type": "string",
                        "description": "推荐类型：personalized(个性化), discovery(探索发现), real_time(实时推荐)",
                        "enum": ["personalized", "discovery", "real_time"],
                        "default": "personalized"
                    },
                    "context": {
                        "type": "object",
                        "description": "上下文信息，如当前查询、最近查询历史等",
                        "default": {}
                    },
                    "limit": {
                        "type": "integer",
                        "description": "推荐数量限制",
                        "default": 10
                    }
                },
                "required": []
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """调用工具"""
    try:
        # 导入我们的应用函数
        from app import add_memory, get_all_memories, delete_all_memories, get_memory, get_entity_relations
        
        user_id = "default_user"
        
        if name == "add_memories":
            text = arguments.get("text", "")
            if not text:
                return [TextContent(type="text", text="错误: 缺少记忆内容")]
            
            result = await add_memory(text, user_id=user_id)
            formatted_result = format_memory_result(result, "add")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "search_memory":
            query = arguments.get("query", "")
            if not query:
                return [TextContent(type="text", text="错误: 缺少搜索查询")]
            
            result = await search_memory(query, user_id=user_id, limit=5)
            formatted_result = format_memory_result(result, "search")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "list_memories":
            result = await get_all_memories(user_id=user_id)
            formatted_result = format_memory_result(result, "list")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "delete_all_memories":
            result = await delete_all_memories(user_id=user_id)
            formatted_result = format_memory_result(result, "delete_all")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "get_memory":
            memory_id = arguments.get("memory_id", "")
            if not memory_id:
                return [TextContent(type="text", text="错误: 缺少记忆ID")]

            result = await get_memory(memory_id=memory_id, user_id=user_id)
            formatted_result = format_memory_result(result, "get")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "get_entity_relations":
            entity_name = arguments.get("entity_name", "")
            if not entity_name:
                return [TextContent(type="text", text="错误: 缺少实体名称")]

            result = await get_entity_relations(entity_name=entity_name, user_id=user_id)
            formatted_result = format_memory_result(result, "entity_relations")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "hybrid_search":
            query = arguments.get("query", "")
            limit = arguments.get("limit", 10)
            if not query:
                return [TextContent(type="text", text="错误: 缺少搜索查询")]

            # 🔧 移除重复的动态实体提取，直接使用 app.py 的功能
            from app import search_memory
            result = await search_memory(query, user_id=user_id, limit=limit)
            formatted_result = format_memory_result(result, "hybrid_search")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "get_intelligent_recommendations":
            # 调用 app.py 中的智能推荐实现
            recommendation_type = arguments.get("type", "personalized")
            context = arguments.get("context", {})
            limit = arguments.get("limit", 10)

            try:
                # 导入 app.py 中的智能推荐函数
                from app import get_intelligent_recommendations

                result = await get_intelligent_recommendations(
                    user_id=user_id,
                    recommendation_type=recommendation_type,
                    context=context,
                    limit=limit
                )

                formatted_result = format_memory_result(result, "intelligent_recommendations")
                return [TextContent(type="text", text=formatted_result)]

            except Exception as e:
                return [TextContent(type="text", text=f"智能推荐失败: {str(e)}")]

        else:
            return [TextContent(type="text", text=f"未知工具: {name}")]
    
    except Exception as e:
        logger.error(f"工具调用失败 {name}: {e}")
        return [TextContent(type="text", text=f"工具调用失败: {e}")]

async def main():
    """主函数"""
    logger.info("启动OpenMemory MCP服务器...")
    
    # 检查依赖
    try:
        from app import add_memory
        logger.info("✓ 应用模块导入成功")
    except Exception as e:
        logger.error(f"✗ 应用模块导入失败: {e}")
        return
    
    # 启动服务器
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
